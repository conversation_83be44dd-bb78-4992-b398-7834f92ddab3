  services:
    app:
      build:
        context: .
        dockerfile: Dockerfile
      depends_on:
        - db
      environment:
        - DATABASE_URL=**********************************/app?serverVersion=13&charset=utf8
      volumes:
        - .:/var/www
    web:
      image: nginx:latest
      ports:
        - "8000:80"
      volumes:
        - ./nginx.conf:/etc/nginx/conf.d/default.conf
        - .:/var/www
      depends_on:
        - app

    db:
      image: postgres:13
      environment:
        POSTGRES_USER: user
        POSTGRES_PASSWORD: password
        POSTGRES_DB: app
      volumes:
        - db_data:/var/lib/postgresql/data
      ports:
        - "5432:5432"

  volumes:
    db_data:
