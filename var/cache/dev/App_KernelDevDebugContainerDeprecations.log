a:12:{i:0;a:6:{s:4:"type";i:16384;s:7:"message";s:148:"Since symfony/framework-bundle 6.1: Not setting the "framework.http_method_override" config option is deprecated. It will default to "false" in 7.0.";s:4:"file";s:78:"/var/www/vendor/symfony/framework-bundle/DependencyInjection/Configuration.php";s:4:"line";i:89;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:65:"/var/www/vendor/symfony/config/Definition/Builder/ExprBuilder.php";s:4:"line";i:246;s:8:"function";s:60:"Symfony\Bundle\FrameworkBundle\DependencyInjection\{closure}";s:5:"class";s:64:"Symfony\Bundle\FrameworkBundle\DependencyInjection\Configuration";s:4:"type";s:2:"->";}}s:5:"count";i:2;}i:1;a:6:{s:4:"type";i:16384;s:7:"message";s:148:"Since symfony/framework-bundle 6.4: Not setting the "framework.handle_all_throwables" config option is deprecated. It will default to "true" in 7.0.";s:4:"file";s:78:"/var/www/vendor/symfony/framework-bundle/DependencyInjection/Configuration.php";s:4:"line";i:94;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:65:"/var/www/vendor/symfony/config/Definition/Builder/ExprBuilder.php";s:4:"line";i:246;s:8:"function";s:60:"Symfony\Bundle\FrameworkBundle\DependencyInjection\{closure}";s:5:"class";s:64:"Symfony\Bundle\FrameworkBundle\DependencyInjection\Configuration";s:4:"type";s:2:"->";}}s:5:"count";i:2;}i:2;a:6:{s:4:"type";i:16384;s:7:"message";s:141:"Since symfony/framework-bundle 6.4: Not setting the "framework.php_errors.log" config option is deprecated. It will default to "true" in 7.0.";s:4:"file";s:78:"/var/www/vendor/symfony/framework-bundle/DependencyInjection/Configuration.php";s:4:"line";i:1342;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:65:"/var/www/vendor/symfony/config/Definition/Builder/ExprBuilder.php";s:4:"line";i:246;s:8:"function";s:60:"Symfony\Bundle\FrameworkBundle\DependencyInjection\{closure}";s:5:"class";s:64:"Symfony\Bundle\FrameworkBundle\DependencyInjection\Configuration";s:4:"type";s:2:"->";}}s:5:"count";i:2;}i:3;a:6:{s:4:"type";i:16384;s:7:"message";s:61:"Please install the "intl" PHP extension for best performance.";s:4:"file";s:83:"/var/www/vendor/symfony/framework-bundle/DependencyInjection/FrameworkExtension.php";s:4:"line";i:305;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:89:"/var/www/vendor/symfony/dependency-injection/Compiler/MergeExtensionConfigurationPass.php";s:4:"line";i:76;s:8:"function";s:4:"load";s:5:"class";s:69:"Symfony\Bundle\FrameworkBundle\DependencyInjection\FrameworkExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:4;a:6:{s:4:"type";i:16384;s:7:"message";s:163:"Since symfony/framework-bundle 6.4: Enabling the integration of Doctrine annotations is deprecated. Set the "framework.annotations.enabled" config option to false.";s:4:"file";s:83:"/var/www/vendor/symfony/framework-bundle/DependencyInjection/FrameworkExtension.php";s:4:"line";i:1792;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:83:"/var/www/vendor/symfony/framework-bundle/DependencyInjection/FrameworkExtension.php";s:4:"line";i:380;s:8:"function";s:32:"registerAnnotationsConfiguration";s:5:"class";s:69:"Symfony\Bundle\FrameworkBundle\DependencyInjection\FrameworkExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:5;a:6:{s:4:"type";i:16384;s:7:"message";s:201:"Since doctrine/doctrine-bundle 2.12: The default value of "doctrine.orm.controller_resolver.auto_mapping" will be changed from `true` to `false`. Explicitly configure `true` to keep existing behaviour.";s:4:"file";s:86:"/var/www/vendor/doctrine/doctrine-bundle/src/DependencyInjection/DoctrineExtension.php";s:4:"line";i:505;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:86:"/var/www/vendor/doctrine/doctrine-bundle/src/DependencyInjection/DoctrineExtension.php";s:4:"line";i:123;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:6;a:6:{s:4:"type";i:16384;s:7:"message";s:173:"Since doctrine/doctrine-bundle 2.13: Enabling the controller resolver automapping feature has been deprecated. Symfony Mapped Route Parameters should be used as replacement.";s:4:"file";s:86:"/var/www/vendor/doctrine/doctrine-bundle/src/DependencyInjection/DoctrineExtension.php";s:4:"line";i:510;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:86:"/var/www/vendor/doctrine/doctrine-bundle/src/DependencyInjection/DoctrineExtension.php";s:4:"line";i:123;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:7;a:6:{s:4:"type";i:16384;s:7:"message";s:112:"Since doctrine/doctrine-bundle 2.11: Not setting "doctrine.orm.enable_lazy_ghost_objects" to true is deprecated.";s:4:"file";s:86:"/var/www/vendor/doctrine/doctrine-bundle/src/DependencyInjection/DoctrineExtension.php";s:4:"line";i:576;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:86:"/var/www/vendor/doctrine/doctrine-bundle/src/DependencyInjection/DoctrineExtension.php";s:4:"line";i:123;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:8;a:6:{s:4:"type";i:16384;s:7:"message";s:208:"Enabling the "sensio_framework_extra.router.annotations" configuration is deprecated since version 5.2. Set it to false and use the "Symfony\Component\Routing\Annotation\Route" annotation from Symfony itself.";s:4:"file";s:103:"/var/www/vendor/sensio/framework-extra-bundle/src/DependencyInjection/SensioFrameworkExtraExtension.php";s:4:"line";i:41;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:89:"/var/www/vendor/symfony/dependency-injection/Compiler/MergeExtensionConfigurationPass.php";s:4:"line";i:76;s:8:"function";s:4:"load";s:5:"class";s:84:"Sensio\Bundle\FrameworkExtraBundle\DependencyInjection\SensioFrameworkExtraExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:9;a:6:{s:4:"type";i:16384;s:7:"message";s:235:"Since symfony/console 6.1: Relying on the static property "$defaultName" for setting a command name is deprecated. Add the "Symfony\Component\Console\Attribute\AsCommand" attribute to the "App\Command\FetchPricesCommand" class instead.";s:4:"file";s:51:"/var/www/vendor/symfony/console/Command/Command.php";s:4:"line";i:85;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:77:"/var/www/vendor/symfony/console/DependencyInjection/AddConsoleCommandPass.php";s:4:"line";i:56;s:8:"function";s:14:"getDefaultName";s:5:"class";s:41:"Symfony\Component\Console\Command\Command";s:4:"type";s:2:"::";}}s:5:"count";i:1;}i:10;a:6:{s:4:"type";i:16384;s:7:"message";s:109:"Since symfony/framework-bundle 6.4: The "annotations.cache_warmer" service is deprecated without replacement.";s:4:"file";s:58:"/var/www/vendor/symfony/deprecation-contracts/function.php";s:4:"line";i:25;s:5:"trace";a:1:{i:0;a:3:{s:4:"file";s:77:"/var/www/var/cache/dev/Container0bznZcM/getAnnotations_CacheWarmerService.php";s:4:"line";i:23;s:8:"function";s:19:"trigger_deprecation";}}s:5:"count";i:1;}i:11;a:6:{s:4:"type";i:16384;s:7:"message";s:103:"Since symfony/framework-bundle 6.4: The "annotations.reader" service is deprecated without replacement.";s:4:"file";s:58:"/var/www/vendor/symfony/deprecation-contracts/function.php";s:4:"line";i:25;s:5:"trace";a:1:{i:0;a:3:{s:4:"file";s:72:"/var/www/var/cache/dev/Container0bznZcM/getAnnotations_ReaderService.php";s:4:"line";i:23;s:8:"function";s:19:"trigger_deprecation";}}s:5:"count";i:1;}}