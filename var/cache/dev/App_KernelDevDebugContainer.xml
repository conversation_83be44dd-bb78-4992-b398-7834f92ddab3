<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <parameters>
    <parameter key="kernel.project_dir">/var/www</parameter>
    <parameter key="kernel.environment">dev</parameter>
    <parameter key="kernel.runtime_environment">%env(default:kernel.environment:APP_RUNTIME_ENV)%</parameter>
    <parameter key="kernel.runtime_mode">%env(query_string:default:container.runtime_mode:APP_RUNTIME_MODE)%</parameter>
    <parameter key="kernel.runtime_mode.web">%env(bool:default::key:web:default:kernel.runtime_mode:)%</parameter>
    <parameter key="kernel.runtime_mode.cli">%env(not:default:kernel.runtime_mode.web:)%</parameter>
    <parameter key="kernel.runtime_mode.worker">%env(bool:default::key:worker:default:kernel.runtime_mode:)%</parameter>
    <parameter key="kernel.debug">true</parameter>
    <parameter key="kernel.build_dir">/var/www/var/cache/dev</parameter>
    <parameter key="kernel.cache_dir">/var/www/var/cache/dev</parameter>
    <parameter key="kernel.logs_dir">/var/www/var/log</parameter>
    <parameter key="kernel.bundles" type="collection">
      <parameter key="FrameworkBundle">Symfony\Bundle\FrameworkBundle\FrameworkBundle</parameter>
      <parameter key="DoctrineBundle">Doctrine\Bundle\DoctrineBundle\DoctrineBundle</parameter>
      <parameter key="DoctrineMigrationsBundle">Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle</parameter>
      <parameter key="SensioFrameworkExtraBundle">Sensio\Bundle\FrameworkExtraBundle\SensioFrameworkExtraBundle</parameter>
    </parameter>
    <parameter key="kernel.bundles_metadata" type="collection">
      <parameter key="FrameworkBundle" type="collection">
        <parameter key="path">/var/www/vendor/symfony/framework-bundle</parameter>
        <parameter key="namespace">Symfony\Bundle\FrameworkBundle</parameter>
      </parameter>
      <parameter key="DoctrineBundle" type="collection">
        <parameter key="path">/var/www/vendor/doctrine/doctrine-bundle</parameter>
        <parameter key="namespace">Doctrine\Bundle\DoctrineBundle</parameter>
      </parameter>
      <parameter key="DoctrineMigrationsBundle" type="collection">
        <parameter key="path">/var/www/vendor/doctrine/doctrine-migrations-bundle</parameter>
        <parameter key="namespace">Doctrine\Bundle\MigrationsBundle</parameter>
      </parameter>
      <parameter key="SensioFrameworkExtraBundle" type="collection">
        <parameter key="path">/var/www/vendor/sensio/framework-extra-bundle/src</parameter>
        <parameter key="namespace">Sensio\Bundle\FrameworkExtraBundle</parameter>
      </parameter>
    </parameter>
    <parameter key="kernel.charset">UTF-8</parameter>
    <parameter key="kernel.container_class">App_KernelDevDebugContainer</parameter>
    <parameter key="event_dispatcher.event_aliases" type="collection">
      <parameter key="Symfony\Component\Console\Event\ConsoleCommandEvent">console.command</parameter>
      <parameter key="Symfony\Component\Console\Event\ConsoleErrorEvent">console.error</parameter>
      <parameter key="Symfony\Component\Console\Event\ConsoleSignalEvent">console.signal</parameter>
      <parameter key="Symfony\Component\Console\Event\ConsoleTerminateEvent">console.terminate</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\ControllerArgumentsEvent">kernel.controller_arguments</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\ControllerEvent">kernel.controller</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\ResponseEvent">kernel.response</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\FinishRequestEvent">kernel.finish_request</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\RequestEvent">kernel.request</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\ViewEvent">kernel.view</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\ExceptionEvent">kernel.exception</parameter>
      <parameter key="Symfony\Component\HttpKernel\Event\TerminateEvent">kernel.terminate</parameter>
    </parameter>
    <parameter key="fragment.renderer.hinclude.global_template">null</parameter>
    <parameter key="fragment.path">/_fragment</parameter>
    <parameter key="kernel.http_method_override">true</parameter>
    <parameter key="kernel.trust_x_sendfile_type_header">false</parameter>
    <parameter key="kernel.trusted_hosts" type="collection"/>
    <parameter key="kernel.default_locale">en</parameter>
    <parameter key="kernel.enabled_locales" type="collection"/>
    <parameter key="kernel.error_controller">error_controller</parameter>
    <parameter key="debug.file_link_format">%env(default::SYMFONY_IDE)%</parameter>
    <parameter key="debug.error_handler.throw_at">-1</parameter>
    <parameter key="debug.container.dump">/var/www/var/cache/dev/App_KernelDevDebugContainer.xml</parameter>
    <parameter key="router.request_context.host">localhost</parameter>
    <parameter key="router.request_context.scheme">http</parameter>
    <parameter key="router.request_context.base_url"></parameter>
    <parameter key="router.resource">kernel::loadRoutes</parameter>
    <parameter key="router.cache_dir">/var/www/var/cache/dev</parameter>
    <parameter key="request_listener.http_port">80</parameter>
    <parameter key="request_listener.https_port">443</parameter>
    <parameter key="cache.prefix.seed">_/var/www.App_KernelDevDebugContainer</parameter>
    <parameter key="data_collector.templates" type="collection"/>
    <parameter key="doctrine.dbal.configuration.class">Doctrine\DBAL\Configuration</parameter>
    <parameter key="doctrine.data_collector.class">Doctrine\Bundle\DoctrineBundle\DataCollector\DoctrineDataCollector</parameter>
    <parameter key="doctrine.dbal.connection.event_manager.class">Symfony\Bridge\Doctrine\ContainerAwareEventManager</parameter>
    <parameter key="doctrine.dbal.connection_factory.class">Doctrine\Bundle\DoctrineBundle\ConnectionFactory</parameter>
    <parameter key="doctrine.dbal.events.mysql_session_init.class">Doctrine\DBAL\Event\Listeners\MysqlSessionInit</parameter>
    <parameter key="doctrine.dbal.events.oracle_session_init.class">Doctrine\DBAL\Event\Listeners\OracleSessionInit</parameter>
    <parameter key="doctrine.class">Doctrine\Bundle\DoctrineBundle\Registry</parameter>
    <parameter key="doctrine.entity_managers" type="collection">
      <parameter key="default">doctrine.orm.default_entity_manager</parameter>
    </parameter>
    <parameter key="doctrine.default_entity_manager">default</parameter>
    <parameter key="doctrine.dbal.connection_factory.types" type="collection">
      <parameter key="date_point" type="collection">
        <parameter key="class">Symfony\Bridge\Doctrine\Types\DatePointType</parameter>
      </parameter>
    </parameter>
    <parameter key="doctrine.connections" type="collection">
      <parameter key="default">doctrine.dbal.default_connection</parameter>
    </parameter>
    <parameter key="doctrine.default_connection">default</parameter>
    <parameter key="doctrine.orm.configuration.class">Doctrine\ORM\Configuration</parameter>
    <parameter key="doctrine.orm.entity_manager.class">Doctrine\ORM\EntityManager</parameter>
    <parameter key="doctrine.orm.manager_configurator.class">Doctrine\Bundle\DoctrineBundle\ManagerConfigurator</parameter>
    <parameter key="doctrine.orm.cache.array.class">Doctrine\Common\Cache\ArrayCache</parameter>
    <parameter key="doctrine.orm.cache.apc.class">Doctrine\Common\Cache\ApcCache</parameter>
    <parameter key="doctrine.orm.cache.memcache.class">Doctrine\Common\Cache\MemcacheCache</parameter>
    <parameter key="doctrine.orm.cache.memcache_host">localhost</parameter>
    <parameter key="doctrine.orm.cache.memcache_port">11211</parameter>
    <parameter key="doctrine.orm.cache.memcache_instance.class">Memcache</parameter>
    <parameter key="doctrine.orm.cache.memcached.class">Doctrine\Common\Cache\MemcachedCache</parameter>
    <parameter key="doctrine.orm.cache.memcached_host">localhost</parameter>
    <parameter key="doctrine.orm.cache.memcached_port">11211</parameter>
    <parameter key="doctrine.orm.cache.memcached_instance.class">Memcached</parameter>
    <parameter key="doctrine.orm.cache.redis.class">Doctrine\Common\Cache\RedisCache</parameter>
    <parameter key="doctrine.orm.cache.redis_host">localhost</parameter>
    <parameter key="doctrine.orm.cache.redis_port">6379</parameter>
    <parameter key="doctrine.orm.cache.redis_instance.class">Redis</parameter>
    <parameter key="doctrine.orm.cache.xcache.class">Doctrine\Common\Cache\XcacheCache</parameter>
    <parameter key="doctrine.orm.cache.wincache.class">Doctrine\Common\Cache\WinCacheCache</parameter>
    <parameter key="doctrine.orm.cache.zenddata.class">Doctrine\Common\Cache\ZendDataCache</parameter>
    <parameter key="doctrine.orm.metadata.driver_chain.class">Doctrine\Persistence\Mapping\Driver\MappingDriverChain</parameter>
    <parameter key="doctrine.orm.metadata.annotation.class">Doctrine\ORM\Mapping\Driver\AnnotationDriver</parameter>
    <parameter key="doctrine.orm.metadata.xml.class">Doctrine\ORM\Mapping\Driver\SimplifiedXmlDriver</parameter>
    <parameter key="doctrine.orm.metadata.yml.class">Doctrine\ORM\Mapping\Driver\SimplifiedYamlDriver</parameter>
    <parameter key="doctrine.orm.metadata.php.class">Doctrine\ORM\Mapping\Driver\PHPDriver</parameter>
    <parameter key="doctrine.orm.metadata.staticphp.class">Doctrine\ORM\Mapping\Driver\StaticPHPDriver</parameter>
    <parameter key="doctrine.orm.metadata.attribute.class">Doctrine\ORM\Mapping\Driver\AttributeDriver</parameter>
    <parameter key="doctrine.orm.proxy_cache_warmer.class">Symfony\Bridge\Doctrine\CacheWarmer\ProxyCacheWarmer</parameter>
    <parameter key="form.type_guesser.doctrine.class">Symfony\Bridge\Doctrine\Form\DoctrineOrmTypeGuesser</parameter>
    <parameter key="doctrine.orm.validator.unique.class">Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntityValidator</parameter>
    <parameter key="doctrine.orm.validator_initializer.class">Symfony\Bridge\Doctrine\Validator\DoctrineInitializer</parameter>
    <parameter key="doctrine.orm.security.user.provider.class">Symfony\Bridge\Doctrine\Security\User\EntityUserProvider</parameter>
    <parameter key="doctrine.orm.listeners.resolve_target_entity.class">Doctrine\ORM\Tools\ResolveTargetEntityListener</parameter>
    <parameter key="doctrine.orm.listeners.attach_entity_listeners.class">Doctrine\ORM\Tools\AttachEntityListenersListener</parameter>
    <parameter key="doctrine.orm.naming_strategy.default.class">Doctrine\ORM\Mapping\DefaultNamingStrategy</parameter>
    <parameter key="doctrine.orm.naming_strategy.underscore.class">Doctrine\ORM\Mapping\UnderscoreNamingStrategy</parameter>
    <parameter key="doctrine.orm.quote_strategy.default.class">Doctrine\ORM\Mapping\DefaultQuoteStrategy</parameter>
    <parameter key="doctrine.orm.quote_strategy.ansi.class">Doctrine\ORM\Mapping\AnsiQuoteStrategy</parameter>
    <parameter key="doctrine.orm.typed_field_mapper.default.class">Doctrine\ORM\Mapping\DefaultTypedFieldMapper</parameter>
    <parameter key="doctrine.orm.entity_listener_resolver.class">Doctrine\Bundle\DoctrineBundle\Mapping\ContainerEntityListenerResolver</parameter>
    <parameter key="doctrine.orm.second_level_cache.default_cache_factory.class">Doctrine\ORM\Cache\DefaultCacheFactory</parameter>
    <parameter key="doctrine.orm.second_level_cache.default_region.class">Doctrine\ORM\Cache\Region\DefaultRegion</parameter>
    <parameter key="doctrine.orm.second_level_cache.filelock_region.class">Doctrine\ORM\Cache\Region\FileLockRegion</parameter>
    <parameter key="doctrine.orm.second_level_cache.logger_chain.class">Doctrine\ORM\Cache\Logging\CacheLoggerChain</parameter>
    <parameter key="doctrine.orm.second_level_cache.logger_statistics.class">Doctrine\ORM\Cache\Logging\StatisticsCacheLogger</parameter>
    <parameter key="doctrine.orm.second_level_cache.cache_configuration.class">Doctrine\ORM\Cache\CacheConfiguration</parameter>
    <parameter key="doctrine.orm.second_level_cache.regions_configuration.class">Doctrine\ORM\Cache\RegionsConfiguration</parameter>
    <parameter key="doctrine.orm.auto_generate_proxy_classes">true</parameter>
    <parameter key="doctrine.orm.enable_lazy_ghost_objects">false</parameter>
    <parameter key="doctrine.orm.proxy_dir">/var/www/var/cache/dev/doctrine/orm/Proxies</parameter>
    <parameter key="doctrine.orm.proxy_namespace">Proxies</parameter>
    <parameter key="doctrine.migrations.preferred_em">null</parameter>
    <parameter key="doctrine.migrations.preferred_connection">null</parameter>
    <parameter key="console.command.ids" type="collection"/>
  </parameters>
  <services>
    <service id="service_container" class="Symfony\Component\DependencyInjection\ContainerInterface" public="true" synthetic="true"/>
    <service id="kernel" class="App\Kernel" public="true" synthetic="true" autoconfigure="true">
      <tag name="controller.service_arguments"/>
      <tag name="routing.route_loader"/>
    </service>
    <service id="App\Entity" class="App\Entity" abstract="true">
      <tag name="container.excluded" source="in &quot;config/services.yaml&quot;"/>
    </service>
    <service id="App\Kernel" class="App\Kernel" abstract="true">
      <tag name="container.excluded" source="in &quot;config/services.yaml&quot;"/>
    </service>
    <service id="App\Command\FetchPricesCommand" class="App\Command\FetchPricesCommand" autowire="true" autoconfigure="true">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="App\Service\PriceFetcherService"/>
      <call method="setName">
        <argument>app:fetch-prices</argument>
      </call>
    </service>
    <service id="App\Controller\PriceController" class="App\Controller\PriceController" public="true" autowire="true" autoconfigure="true">
      <tag name="controller.service_arguments"/>
      <tag name="container.service_subscriber"/>
      <argument type="service" id="logger"/>
      <argument type="service" id="App\Repository\ProductPriceRepository"/>
      <call method="setContainer">
        <argument type="service" id=".service_locator.O2p6Lk7.App\Controller\PriceController"/>
      </call>
    </service>
    <service id="App\Dto\PriceData" class="App\Dto\PriceData" autowire="true" autoconfigure="true">
      <tag name="container.error" message="Cannot autowire service &quot;App\Dto\PriceData&quot;: argument &quot;$productId&quot; of method &quot;__construct()&quot; is type-hinted &quot;string&quot;, you should configure its value explicitly."/>
    </service>
    <service id="App\Mapper\Api1Mapper" class="App\Mapper\Api1Mapper" autowire="true" autoconfigure="true"/>
    <service id="App\Mapper\Api2Mapper" class="App\Mapper\Api2Mapper" autowire="true" autoconfigure="true"/>
    <service id="App\Middleware\AuthMiddleware" class="App\Middleware\AuthMiddleware" autowire="true" autoconfigure="true">
      <tag name="kernel.event_listener" event="kernel.request" method="handle"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="logger"/>
    </service>
    <service id="App\Repository\ProductPriceRepository" class="App\Repository\ProductPriceRepository" autowire="true" autoconfigure="true">
      <tag name="doctrine.repository_service"/>
      <argument type="service" id="doctrine"/>
    </service>
    <service id="App\Service\MockApiService" class="App\Service\MockApiService" autowire="true" autoconfigure="true">
      <argument type="service" id="logger"/>
    </service>
    <service id="App\Service\PriceFetcherService" class="App\Service\PriceFetcherService" autowire="true" autoconfigure="true">
      <argument type="service" id="App\Service\MockApiService"/>
      <argument type="service" id="App\Repository\ProductPriceRepository"/>
      <argument type="service" id="logger"/>
      <argument type="collection">
        <argument key="api1" type="service" id="App\Mapper\Api1Mapper"/>
        <argument key="api2" type="service" id="App\Mapper\Api2Mapper"/>
      </argument>
    </service>
    <service id="monolog.logger" class="Monolog\Logger" autowire="true" autoconfigure="true">
      <argument>app</argument>
      <call method="pushHandler">
        <argument type="service" id="monolog.handler.main"/>
      </call>
    </service>
    <service id="App\Mapper\ApiMapperInterface" class="App\Mapper\ApiMapperInterface" autowire="true" autoconfigure="true"/>
    <service id="argument_metadata_factory" class="Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory"/>
    <service id="argument_resolver.backed_enum_resolver" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver">
      <tag priority="100" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver">controller.argument_value_resolver</tag>
    </service>
    <service id="argument_resolver.datetime" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver">
      <tag priority="100" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver">controller.argument_value_resolver</tag>
      <argument type="service" id="clock" on-invalid="null"/>
    </service>
    <service id="argument_resolver.request_payload" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestPayloadValueResolver" lazy="true">
      <tag name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestPayloadValueResolver">controller.targeted_value_resolver</tag>
      <tag name="container.error" message="You can neither use &quot;#[MapRequestPayload]&quot; nor &quot;#[MapQueryString]&quot; since the Serializer component is not installed. Try running &quot;composer require symfony/serializer-pack&quot;."/>
    </service>
    <service id="argument_resolver.request_attribute" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver">
      <tag priority="100" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver">controller.argument_value_resolver</tag>
    </service>
    <service id="argument_resolver.request" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver">
      <tag priority="50" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver">controller.argument_value_resolver</tag>
    </service>
    <service id="argument_resolver.session" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver">
      <tag priority="50" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver">controller.argument_value_resolver</tag>
    </service>
    <service id="argument_resolver.service" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver">
      <tag priority="-50" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver">controller.argument_value_resolver</tag>
      <argument type="service" id=".service_locator.2JZTdhn"/>
    </service>
    <service id="argument_resolver.default" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver">
      <tag priority="-100" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver">controller.argument_value_resolver</tag>
    </service>
    <service id="argument_resolver.variadic" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver">
      <tag priority="-150" name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver">controller.argument_value_resolver</tag>
    </service>
    <service id="argument_resolver.query_parameter_value_resolver" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver">
      <tag name="Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver">controller.targeted_value_resolver</tag>
    </service>
    <service id="response_listener" class="Symfony\Component\HttpKernel\EventListener\ResponseListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <argument>UTF-8</argument>
      <argument>false</argument>
    </service>
    <service id="locale_listener" class="Symfony\Component\HttpKernel\EventListener\LocaleListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <tag name="container.hot_path"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="request_stack"/>
      <argument>en</argument>
      <argument type="service" id="router.default" on-invalid="ignore"/>
      <argument>false</argument>
      <argument type="collection"/>
    </service>
    <service id="validate_request_listener" class="Symfony\Component\HttpKernel\EventListener\ValidateRequestListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
    </service>
    <service id="disallow_search_engine_index_response_listener" class="Symfony\Component\HttpKernel\EventListener\DisallowRobotsIndexingListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
    </service>
    <service id="error_controller" class="Symfony\Component\HttpKernel\Controller\ErrorController" public="true">
      <argument type="service" id="http_kernel"/>
      <argument>error_controller</argument>
      <argument type="service" id="error_handler.error_renderer.html"/>
    </service>
    <service id="exception_listener" class="Symfony\Component\HttpKernel\EventListener\ErrorListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="monolog.logger" channel="request"/>
      <tag name="container.hot_path"/>
      <tag name="container.hot_path"/>
      <argument>error_controller</argument>
      <argument type="service" id="logger" on-invalid="null"/>
      <argument>true</argument>
      <argument type="collection"/>
    </service>
    <service id="controller.cache_attribute_listener" class="Symfony\Component\HttpKernel\EventListener\CacheAttributeListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <tag name="container.hot_path"/>
    </service>
    <service id="parameter_bag" class="Symfony\Component\DependencyInjection\ParameterBag\ContainerBag">
      <argument type="service" id="service_container"/>
    </service>
    <service id="http_kernel" class="Symfony\Component\HttpKernel\HttpKernel" public="true">
      <tag name="container.hot_path"/>
      <tag name="container.preload" class="Symfony\Component\Runtime\Runner\Symfony\HttpKernelRunner"/>
      <tag name="container.preload" class="Symfony\Component\Runtime\Runner\Symfony\ResponseRunner"/>
      <tag name="container.preload" class="Symfony\Component\Runtime\SymfonyRuntime"/>
      <argument type="service" id="debug.event_dispatcher"/>
      <argument type="service" id="debug.controller_resolver"/>
      <argument type="service" id="request_stack"/>
      <argument type="service" id="debug.argument_resolver"/>
      <argument>false</argument>
    </service>
    <service id="request_stack" class="Symfony\Component\HttpFoundation\RequestStack" public="true">
      <tag name="kernel.reset" method="resetRequestFormats" on_invalid="ignore"/>
    </service>
    <service id="http_cache" class="Symfony\Bundle\FrameworkBundle\HttpCache\HttpCache">
      <tag name="container.hot_path"/>
      <argument type="service" id="kernel"/>
      <argument type="service" id="http_cache.store"/>
      <argument>null</argument>
      <argument type="collection">
        <argument key="debug">true</argument>
      </argument>
      <argument type="service">
        <service class="void">
          <factory class="Symfony\Component\HttpFoundation\Request" method="enableHttpMethodParameterOverride"/>
        </service>
      </argument>
    </service>
    <service id="http_cache.store" class="Symfony\Component\HttpKernel\HttpCache\Store">
      <argument>/var/www/var/cache/dev/http_cache</argument>
    </service>
    <service id="url_helper" class="Symfony\Component\HttpFoundation\UrlHelper">
      <argument type="service" id="request_stack"/>
      <argument type="service" id="router.default" on-invalid="ignore"/>
    </service>
    <service id="cache_warmer" class="Symfony\Component\HttpKernel\CacheWarmer\CacheWarmerAggregate" public="true">
      <tag name="container.no_preload"/>
      <argument type="tagged_iterator" tag="kernel.cache_warmer"/>
      <argument>true</argument>
      <argument>/var/www/var/cache/dev/App_KernelDevDebugContainerDeprecations.log</argument>
    </service>
    <service id="cache_clearer" class="Symfony\Component\HttpKernel\CacheClearer\ChainCacheClearer">
      <argument type="tagged_iterator" tag="kernel.cache_clearer"/>
    </service>
    <service id="filesystem" class="Symfony\Component\Filesystem\Filesystem"/>
    <service id="file_locator" class="Symfony\Component\HttpKernel\Config\FileLocator">
      <argument type="service" id="kernel"/>
    </service>
    <service id="uri_signer" class="Symfony\Component\HttpFoundation\UriSigner">
      <tag name="container.error" message="You have requested a non-existent parameter &quot;kernel.secret&quot;. Did you mean this: &quot;kernel.charset&quot;?"/>
      <argument>null</argument>
    </service>
    <service id="config_cache_factory" class="Symfony\Component\Config\ResourceCheckerConfigCacheFactory">
      <argument type="tagged_iterator" tag="config_cache.resource_checker"/>
    </service>
    <service id="dependency_injection.config.container_parameters_resource_checker" class="Symfony\Component\DependencyInjection\Config\ContainerParametersResourceChecker">
      <tag name="config_cache.resource_checker" priority="-980"/>
      <argument type="service" id="service_container"/>
    </service>
    <service id="config.resource.self_checking_resource_checker" class="Symfony\Component\Config\Resource\SelfCheckingResourceChecker">
      <tag name="config_cache.resource_checker" priority="-990"/>
    </service>
    <service id="services_resetter" class="Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter" public="true">
      <argument type="iterator">
        <argument key="request_stack" type="service" id="request_stack" on-invalid="ignore_uninitialized"/>
        <argument key="cache.app" type="service" id="cache.app" on-invalid="ignore_uninitialized"/>
        <argument key="cache.system" type="service" id="cache.system" on-invalid="ignore_uninitialized"/>
        <argument key="cache.validator" type="service" id="cache.validator" on-invalid="ignore_uninitialized"/>
        <argument key="cache.serializer" type="service" id="cache.serializer" on-invalid="ignore_uninitialized"/>
        <argument key="cache.annotations" type="service" id="cache.annotations" on-invalid="ignore_uninitialized"/>
        <argument key="cache.property_info" type="service" id="cache.property_info" on-invalid="ignore_uninitialized"/>
        <argument key="debug.stopwatch" type="service" id="debug.stopwatch" on-invalid="ignore_uninitialized"/>
        <argument key="debug.event_dispatcher" type="service" id="debug.event_dispatcher" on-invalid="ignore_uninitialized"/>
        <argument key="doctrine" type="service" id="doctrine" on-invalid="ignore_uninitialized"/>
        <argument key="doctrine.debug_data_holder" type="service" id="doctrine.debug_data_holder" on-invalid="ignore_uninitialized"/>
      </argument>
      <argument type="collection">
        <argument key="request_stack" type="collection">
          <argument>?resetRequestFormats</argument>
        </argument>
        <argument key="cache.app" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="cache.system" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="cache.validator" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="cache.serializer" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="cache.annotations" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="cache.property_info" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="debug.stopwatch" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="debug.event_dispatcher" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="doctrine" type="collection">
          <argument>reset</argument>
        </argument>
        <argument key="doctrine.debug_data_holder" type="collection">
          <argument>reset</argument>
        </argument>
      </argument>
    </service>
    <service id="reverse_container" class="Symfony\Component\DependencyInjection\ReverseContainer">
      <argument type="service" id="service_container"/>
      <argument type="service" id=".service_locator.XXv1IfR"/>
    </service>
    <service id="container.env_var_processor" class="Symfony\Component\DependencyInjection\EnvVarProcessor">
      <tag name="container.env_var_processor"/>
      <argument type="service" id="service_container"/>
      <argument type="tagged_iterator" tag="container.env_var_loader"/>
    </service>
    <service id="slugger" class="Symfony\Component\String\Slugger\SluggerInterface">
      <tag name="container.error" message="You cannot use the &quot;slugger&quot; service since the Translation contracts are not installed. Try running &quot;composer require symfony/translation&quot;."/>
    </service>
    <service id="container.getenv" class="Closure">
      <tag name="routing.expression_language_function" function="env"/>
      <argument type="collection">
        <argument type="service" id="service_container"/>
        <argument>getEnv</argument>
      </argument>
      <factory class="Closure" method="fromCallable"/>
    </service>
    <service id="container.get_routing_condition_service" class="Closure" public="true">
      <tag name="routing.expression_language_function" function="service"/>
      <argument type="collection">
        <argument type="service" id=".service_locator.Xbsa8iG"/>
        <argument>get</argument>
      </argument>
      <factory class="Closure" method="fromCallable"/>
    </service>
    <service id="container.env" class="Symfony\Component\String\LazyString" constructor="fromCallable" abstract="true">
      <argument type="service" id="container.getenv"/>
    </service>
    <service id="config_builder.warmer" class="Symfony\Bundle\FrameworkBundle\CacheWarmer\ConfigBuilderCacheWarmer">
      <tag name="kernel.cache_warmer"/>
      <argument type="service" id="kernel"/>
      <argument type="service" id="logger" on-invalid="null"/>
    </service>
    <service id="clock" class="Symfony\Component\Clock\Clock"/>
    <service id="Symfony\Component\Config\Loader\LoaderInterface" class="Symfony\Component\Config\Loader\LoaderInterface" abstract="true">
      <tag name="container.excluded"/>
    </service>
    <service id="Symfony\Component\HttpFoundation\Request" class="Symfony\Component\HttpFoundation\Request" abstract="true">
      <tag name="container.excluded"/>
    </service>
    <service id="Symfony\Component\HttpFoundation\Response" class="Symfony\Component\HttpFoundation\Response" abstract="true">
      <tag name="container.excluded"/>
    </service>
    <service id="Symfony\Component\HttpFoundation\Session\SessionInterface" class="Symfony\Component\HttpFoundation\Session\SessionInterface" abstract="true">
      <tag name="container.excluded"/>
    </service>
    <service id="fragment.handler" class="Symfony\Component\HttpKernel\DependencyInjection\LazyLoadingFragmentHandler">
      <argument type="service" id=".service_locator.lLv4pWF"/>
      <argument type="service" id="request_stack"/>
      <argument>true</argument>
    </service>
    <service id="fragment.uri_generator" class="Symfony\Component\HttpKernel\Fragment\FragmentUriGenerator">
      <argument>/_fragment</argument>
      <argument type="service" id="uri_signer"/>
      <argument type="service" id="request_stack"/>
    </service>
    <service id="fragment.renderer.inline" class="Symfony\Component\HttpKernel\Fragment\InlineFragmentRenderer">
      <tag name="kernel.fragment_renderer" alias="inline"/>
      <argument type="service" id="http_kernel"/>
      <argument type="service" id="debug.event_dispatcher"/>
      <call method="setFragmentPath">
        <argument>/_fragment</argument>
      </call>
    </service>
    <service id="error_handler.error_renderer.html" class="Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer">
      <argument type="service">
        <service>
          <argument type="service" id="request_stack"/>
          <argument>true</argument>
          <factory class="Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer" method="isDebug"/>
        </service>
      </argument>
      <argument>UTF-8</argument>
      <argument type="service" id="debug.file_link_formatter" on-invalid="null"/>
      <argument>/var/www</argument>
      <argument type="service">
        <service>
          <argument type="service" id="request_stack"/>
          <factory class="Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer" method="getAndCleanOutputBuffer"/>
        </service>
      </argument>
      <argument type="service" id="logger" on-invalid="null"/>
    </service>
    <service id="console.error_listener" class="Symfony\Component\Console\EventListener\ErrorListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="monolog.logger" channel="console"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="logger" on-invalid="null"/>
    </service>
    <service id="console.suggest_missing_package_subscriber" class="Symfony\Bundle\FrameworkBundle\EventListener\SuggestMissingPackageSubscriber">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.no_preload"/>
    </service>
    <service id="console.command.about" class="Symfony\Bundle\FrameworkBundle\Command\AboutCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <call method="setName">
        <argument>about</argument>
      </call>
      <call method="setDescription">
        <argument>Display information about the current project</argument>
      </call>
    </service>
    <service id="console.command.assets_install" class="Symfony\Bundle\FrameworkBundle\Command\AssetsInstallCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="filesystem"/>
      <argument>/var/www</argument>
      <call method="setName">
        <argument>assets:install</argument>
      </call>
      <call method="setDescription">
        <argument>Install bundle's web assets under a public directory</argument>
      </call>
    </service>
    <service id="console.command.cache_clear" class="Symfony\Bundle\FrameworkBundle\Command\CacheClearCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="cache_clearer"/>
      <argument type="service" id="filesystem"/>
      <call method="setName">
        <argument>cache:clear</argument>
      </call>
      <call method="setDescription">
        <argument>Clear the cache</argument>
      </call>
    </service>
    <service id="console.command.cache_pool_clear" class="Symfony\Bundle\FrameworkBundle\Command\CachePoolClearCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="cache.global_clearer"/>
      <argument type="collection">
        <argument>cache.app</argument>
        <argument>cache.system</argument>
        <argument>cache.validator</argument>
        <argument>cache.serializer</argument>
        <argument>cache.annotations</argument>
        <argument>cache.property_info</argument>
        <argument>cache.doctrine.orm.default.result</argument>
        <argument>cache.doctrine.orm.default.query</argument>
      </argument>
      <call method="setName">
        <argument>cache:pool:clear</argument>
      </call>
      <call method="setDescription">
        <argument>Clear cache pools</argument>
      </call>
    </service>
    <service id="console.command.cache_pool_prune" class="Symfony\Bundle\FrameworkBundle\Command\CachePoolPruneCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="collection"/>
      <call method="setName">
        <argument>cache:pool:prune</argument>
      </call>
      <call method="setDescription">
        <argument>Prune cache pools</argument>
      </call>
    </service>
    <service id="console.command.cache_pool_invalidate_tags" class="Symfony\Bundle\FrameworkBundle\Command\CachePoolInvalidateTagsCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id=".service_locator.NBUFN6A"/>
      <call method="setName">
        <argument>cache:pool:invalidate-tags</argument>
      </call>
      <call method="setDescription">
        <argument>Invalidate cache tags for all or a specific pool</argument>
      </call>
    </service>
    <service id="console.command.cache_pool_delete" class="Symfony\Bundle\FrameworkBundle\Command\CachePoolDeleteCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="cache.global_clearer"/>
      <argument type="collection">
        <argument>cache.app</argument>
        <argument>cache.system</argument>
        <argument>cache.validator</argument>
        <argument>cache.serializer</argument>
        <argument>cache.annotations</argument>
        <argument>cache.property_info</argument>
        <argument>cache.doctrine.orm.default.result</argument>
        <argument>cache.doctrine.orm.default.query</argument>
      </argument>
      <call method="setName">
        <argument>cache:pool:delete</argument>
      </call>
      <call method="setDescription">
        <argument>Delete an item from a cache pool</argument>
      </call>
    </service>
    <service id="console.command.cache_pool_list" class="Symfony\Bundle\FrameworkBundle\Command\CachePoolListCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="collection">
        <argument>cache.app</argument>
        <argument>cache.system</argument>
        <argument>cache.validator</argument>
        <argument>cache.serializer</argument>
        <argument>cache.annotations</argument>
        <argument>cache.property_info</argument>
        <argument>cache.doctrine.orm.default.result</argument>
        <argument>cache.doctrine.orm.default.query</argument>
      </argument>
      <call method="setName">
        <argument>cache:pool:list</argument>
      </call>
      <call method="setDescription">
        <argument>List available cache pools</argument>
      </call>
    </service>
    <service id="console.command.cache_warmup" class="Symfony\Bundle\FrameworkBundle\Command\CacheWarmupCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="cache_warmer"/>
      <call method="setName">
        <argument>cache:warmup</argument>
      </call>
      <call method="setDescription">
        <argument>Warm up an empty cache</argument>
      </call>
    </service>
    <service id="console.command.config_debug" class="Symfony\Bundle\FrameworkBundle\Command\ConfigDebugCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <call method="setName">
        <argument>debug:config</argument>
      </call>
      <call method="setDescription">
        <argument>Dump the current configuration for an extension</argument>
      </call>
    </service>
    <service id="console.command.config_dump_reference" class="Symfony\Bundle\FrameworkBundle\Command\ConfigDumpReferenceCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <call method="setName">
        <argument>config:dump-reference</argument>
      </call>
      <call method="setDescription">
        <argument>Dump the default configuration for an extension</argument>
      </call>
    </service>
    <service id="console.command.container_debug" class="Symfony\Bundle\FrameworkBundle\Command\ContainerDebugCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <call method="setName">
        <argument>debug:container</argument>
      </call>
      <call method="setDescription">
        <argument>Display current services for an application</argument>
      </call>
    </service>
    <service id="console.command.container_lint" class="Symfony\Bundle\FrameworkBundle\Command\ContainerLintCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <call method="setName">
        <argument>lint:container</argument>
      </call>
      <call method="setDescription">
        <argument>Ensure that arguments injected into services match type declarations</argument>
      </call>
    </service>
    <service id="console.command.debug_autowiring" class="Symfony\Bundle\FrameworkBundle\Command\DebugAutowiringCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument>null</argument>
      <argument type="service" id="debug.file_link_formatter" on-invalid="null"/>
      <call method="setName">
        <argument>debug:autowiring</argument>
      </call>
      <call method="setDescription">
        <argument>List classes/interfaces you can use for autowiring</argument>
      </call>
    </service>
    <service id="console.command.event_dispatcher_debug" class="Symfony\Bundle\FrameworkBundle\Command\EventDispatcherDebugCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id=".service_locator.9ETxUxh"/>
      <call method="setName">
        <argument>debug:event-dispatcher</argument>
      </call>
      <call method="setDescription">
        <argument>Display configured listeners for an application</argument>
      </call>
    </service>
    <service id="console.command.router_debug" class="Symfony\Bundle\FrameworkBundle\Command\RouterDebugCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="router.default"/>
      <argument type="service" id="debug.file_link_formatter" on-invalid="null"/>
      <call method="setName">
        <argument>debug:router</argument>
      </call>
      <call method="setDescription">
        <argument>Display current routes for an application</argument>
      </call>
    </service>
    <service id="console.command.router_match" class="Symfony\Bundle\FrameworkBundle\Command\RouterMatchCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="router.default"/>
      <argument type="tagged_iterator" tag="routing.expression_language_provider"/>
      <call method="setName">
        <argument>router:match</argument>
      </call>
      <call method="setDescription">
        <argument>Help debug routes by simulating a path info match</argument>
      </call>
    </service>
    <service id="console.command.yaml_lint" class="Symfony\Bundle\FrameworkBundle\Command\YamlLintCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <call method="setName">
        <argument>lint:yaml</argument>
      </call>
      <call method="setDescription">
        <argument>Lint a YAML file and outputs encountered errors</argument>
      </call>
    </service>
    <service id="console.command.secrets_set" class="Symfony\Bundle\FrameworkBundle\Command\SecretsSetCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="secrets.vault"/>
      <argument type="service" id="secrets.local_vault" on-invalid="null"/>
      <call method="setName">
        <argument>secrets:set</argument>
      </call>
      <call method="setDescription">
        <argument>Set a secret in the vault</argument>
      </call>
    </service>
    <service id="console.command.secrets_remove" class="Symfony\Bundle\FrameworkBundle\Command\SecretsRemoveCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="secrets.vault"/>
      <argument type="service" id="secrets.local_vault" on-invalid="null"/>
      <call method="setName">
        <argument>secrets:remove</argument>
      </call>
      <call method="setDescription">
        <argument>Remove a secret from the vault</argument>
      </call>
    </service>
    <service id="console.command.secrets_generate_key" class="Symfony\Bundle\FrameworkBundle\Command\SecretsGenerateKeysCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="secrets.vault"/>
      <argument type="service" id="secrets.local_vault" on-invalid="ignore"/>
      <call method="setName">
        <argument>secrets:generate-keys</argument>
      </call>
      <call method="setDescription">
        <argument>Generate new encryption keys</argument>
      </call>
    </service>
    <service id="console.command.secrets_list" class="Symfony\Bundle\FrameworkBundle\Command\SecretsListCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="secrets.vault"/>
      <argument type="service" id="secrets.local_vault" on-invalid="ignore"/>
      <call method="setName">
        <argument>secrets:list</argument>
      </call>
      <call method="setDescription">
        <argument>List all secrets</argument>
      </call>
    </service>
    <service id="console.command.secrets_decrypt_to_local" class="Symfony\Bundle\FrameworkBundle\Command\SecretsDecryptToLocalCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="secrets.vault"/>
      <argument type="service" id="secrets.local_vault" on-invalid="ignore"/>
      <call method="setName">
        <argument>secrets:decrypt-to-local</argument>
      </call>
      <call method="setDescription">
        <argument>Decrypt all secrets and stores them in the local vault</argument>
      </call>
    </service>
    <service id="console.command.secrets_encrypt_from_local" class="Symfony\Bundle\FrameworkBundle\Command\SecretsEncryptFromLocalCommand">
      <tag name="console.command"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="secrets.vault"/>
      <argument type="service" id="secrets.local_vault" on-invalid="ignore"/>
      <call method="setName">
        <argument>secrets:encrypt-from-local</argument>
      </call>
      <call method="setDescription">
        <argument>Encrypt all local secrets to the vault</argument>
      </call>
    </service>
    <service id="console.messenger.application" class="Symfony\Bundle\FrameworkBundle\Console\Application" shared="false">
      <argument type="service" id="kernel"/>
      <call method="setAutoExit">
        <argument>false</argument>
      </call>
    </service>
    <service id="console.messenger.execute_command_handler" class="Symfony\Component\Console\Messenger\RunCommandMessageHandler">
      <tag name="messenger.message_handler"/>
      <argument type="service" id="console.messenger.application"/>
    </service>
    <service id="cache.app" class="Symfony\Component\Cache\Adapter\FilesystemAdapter" public="true">
      <tag name="cache.pool"/>
      <tag name="kernel.reset" method="reset"/>
      <argument>LuAv8pQCYe</argument>
      <argument>0</argument>
      <argument>/var/www/var/cache/dev/pools/app</argument>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.app.taggable" class="Symfony\Component\Cache\Adapter\TagAwareAdapter">
      <tag name="cache.taggable" pool="cache.app"/>
      <argument type="service" id="cache.app"/>
    </service>
    <service id="cache.system" class="Symfony\Component\Cache\Adapter\AdapterInterface" public="true">
      <tag name="cache.pool"/>
      <tag name="kernel.reset" method="reset"/>
      <argument>bccsZj9nKy</argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <argument>/var/www/var/cache/dev/pools/system</argument>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <factory class="Symfony\Component\Cache\Adapter\AbstractAdapter" method="createSystemCache"/>
    </service>
    <service id="cache.validator" class="Symfony\Component\Cache\Adapter\AdapterInterface">
      <tag name="cache.pool"/>
      <tag name="kernel.reset" method="reset"/>
      <argument>z74AzGDfgU</argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <argument>/var/www/var/cache/dev/pools/system</argument>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <factory class="Symfony\Component\Cache\Adapter\AbstractAdapter" method="createSystemCache"/>
    </service>
    <service id="cache.serializer" class="Symfony\Component\Cache\Adapter\AdapterInterface">
      <tag name="cache.pool"/>
      <tag name="kernel.reset" method="reset"/>
      <argument>j+ucIwYDJv</argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <argument>/var/www/var/cache/dev/pools/system</argument>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <factory class="Symfony\Component\Cache\Adapter\AbstractAdapter" method="createSystemCache"/>
    </service>
    <service id="cache.annotations" class="Symfony\Component\Cache\Adapter\AdapterInterface">
      <tag name="cache.pool"/>
      <tag name="kernel.reset" method="reset"/>
      <argument>Ff+PPxzp-Z</argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <argument>/var/www/var/cache/dev/pools/system</argument>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <factory class="Symfony\Component\Cache\Adapter\AbstractAdapter" method="createSystemCache"/>
    </service>
    <service id="cache.property_info" class="Symfony\Component\Cache\Adapter\AdapterInterface">
      <tag name="cache.pool"/>
      <tag name="kernel.reset" method="reset"/>
      <argument>d1r-ZqWbcx</argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <argument>/var/www/var/cache/dev/pools/system</argument>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <factory class="Symfony\Component\Cache\Adapter\AbstractAdapter" method="createSystemCache"/>
    </service>
    <service id="cache.adapter.system" class="Symfony\Component\Cache\Adapter\AdapterInterface" abstract="true">
      <tag name="cache.pool" clearer="cache.system_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument></argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <argument>/var/www/var/cache/dev/pools/system</argument>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <factory class="Symfony\Component\Cache\Adapter\AbstractAdapter" method="createSystemCache"/>
    </service>
    <service id="cache.adapter.apcu" class="Symfony\Component\Cache\Adapter\ApcuAdapter" abstract="true">
      <tag name="cache.pool" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument></argument>
      <argument>0</argument>
      <argument>%container.build_id%</argument>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.filesystem" class="Symfony\Component\Cache\Adapter\FilesystemAdapter" abstract="true">
      <tag name="cache.pool" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument></argument>
      <argument>0</argument>
      <argument>/var/www/var/cache/dev/pools/app</argument>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.psr6" class="Symfony\Component\Cache\Adapter\ProxyAdapter" abstract="true">
      <tag name="cache.pool" provider="cache.default_psr6_provider" clearer="cache.default_clearer" reset="reset"/>
      <argument type="abstract">PSR-6 provider service</argument>
      <argument></argument>
      <argument>0</argument>
    </service>
    <service id="cache.adapter.redis" class="Symfony\Component\Cache\Adapter\RedisAdapter" abstract="true">
      <tag name="cache.pool" provider="cache.default_redis_provider" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument type="abstract">Redis connection service</argument>
      <argument></argument>
      <argument>0</argument>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.redis_tag_aware" class="Symfony\Component\Cache\Adapter\RedisTagAwareAdapter" abstract="true">
      <tag name="cache.pool" provider="cache.default_redis_provider" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument type="abstract">Redis connection service</argument>
      <argument></argument>
      <argument>0</argument>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.memcached" class="Symfony\Component\Cache\Adapter\MemcachedAdapter" abstract="true">
      <tag name="cache.pool" provider="cache.default_memcached_provider" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument type="abstract">Memcached connection service</argument>
      <argument></argument>
      <argument>0</argument>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.doctrine_dbal" class="Symfony\Component\Cache\Adapter\DoctrineDbalAdapter" abstract="true">
      <tag name="cache.pool" provider="cache.default_doctrine_dbal_provider" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument type="abstract">DBAL connection service</argument>
      <argument></argument>
      <argument>0</argument>
      <argument type="collection"/>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.pdo" class="Symfony\Component\Cache\Adapter\PdoAdapter" abstract="true">
      <tag name="cache.pool" provider="cache.default_pdo_provider" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument type="abstract">PDO connection service</argument>
      <argument></argument>
      <argument>0</argument>
      <argument type="collection"/>
      <argument type="service" id="cache.default_marshaller" on-invalid="ignore"/>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.adapter.array" class="Symfony\Component\Cache\Adapter\ArrayAdapter" abstract="true">
      <tag name="cache.pool" clearer="cache.default_clearer" reset="reset"/>
      <tag name="monolog.logger" channel="cache"/>
      <argument>0</argument>
      <call method="setLogger">
        <argument type="service" id="logger" on-invalid="ignore"/>
      </call>
    </service>
    <service id="cache.default_marshaller" class="Symfony\Component\Cache\Marshaller\DefaultMarshaller">
      <argument>null</argument>
      <argument>true</argument>
    </service>
    <service id="cache.default_clearer" class="Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer">
      <tag name="cache.pool.clearer"/>
      <argument type="collection">
        <argument key="cache.app" type="service" id="cache.app" on-invalid="ignore_uninitialized"/>
      </argument>
    </service>
    <service id="cache.system_clearer" class="Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer" public="true">
      <tag name="cache.pool.clearer"/>
      <argument type="collection">
        <argument key="cache.system" type="service" id="cache.system" on-invalid="ignore_uninitialized"/>
        <argument key="cache.validator" type="service" id="cache.validator" on-invalid="ignore_uninitialized"/>
        <argument key="cache.serializer" type="service" id="cache.serializer" on-invalid="ignore_uninitialized"/>
        <argument key="cache.annotations" type="service" id="cache.annotations" on-invalid="ignore_uninitialized"/>
        <argument key="cache.property_info" type="service" id="cache.property_info" on-invalid="ignore_uninitialized"/>
      </argument>
    </service>
    <service id="cache.global_clearer" class="Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer" public="true">
      <tag name="cache.pool.clearer"/>
      <argument type="collection">
        <argument key="cache.app" type="service" id="cache.app" on-invalid="ignore_uninitialized"/>
        <argument key="cache.system" type="service" id="cache.system" on-invalid="ignore_uninitialized"/>
        <argument key="cache.validator" type="service" id="cache.validator" on-invalid="ignore_uninitialized"/>
        <argument key="cache.serializer" type="service" id="cache.serializer" on-invalid="ignore_uninitialized"/>
        <argument key="cache.annotations" type="service" id="cache.annotations" on-invalid="ignore_uninitialized"/>
        <argument key="cache.property_info" type="service" id="cache.property_info" on-invalid="ignore_uninitialized"/>
        <argument key="cache.doctrine.orm.default.result" type="service" id="cache.doctrine.orm.default.result" on-invalid="ignore_uninitialized"/>
        <argument key="cache.doctrine.orm.default.query" type="service" id="cache.doctrine.orm.default.query" on-invalid="ignore_uninitialized"/>
      </argument>
    </service>
    <service id="debug.error_handler_configurator" class="Symfony\Component\HttpKernel\Debug\ErrorHandlerConfigurator" public="true">
      <tag name="monolog.logger" channel="php"/>
      <argument type="service" id="logger" on-invalid="null"/>
      <argument>null</argument>
      <argument>-1</argument>
      <argument>true</argument>
      <argument>true</argument>
      <argument>null</argument>
    </service>
    <service id="debug.debug_handlers_listener" class="Symfony\Component\HttpKernel\EventListener\DebugHandlersListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <argument>null</argument>
      <argument>%env(bool:default::key:web:default:kernel.runtime_mode:)%</argument>
    </service>
    <service id="debug.file_link_formatter" class="Symfony\Component\ErrorHandler\ErrorRenderer\FileLinkFormatter">
      <argument>%env(default::SYMFONY_IDE)%</argument>
    </service>
    <service id="debug.stopwatch" class="Symfony\Component\Stopwatch\Stopwatch" public="true">
      <tag name="kernel.reset" method="reset"/>
      <argument>true</argument>
    </service>
    <service id="debug.event_dispatcher" class="Symfony\Component\HttpKernel\Debug\TraceableEventDispatcher">
      <tag name="container.hot_path"/>
      <tag name="event_dispatcher">event_dispatcher.dispatcher</tag>
      <tag name="monolog.logger" channel="event"/>
      <tag name="kernel.reset" method="reset"/>
      <tag name="container.decorator" id="event_dispatcher" inner="debug.event_dispatcher.inner"/>
      <argument type="service" id="debug.event_dispatcher.inner"/>
      <argument type="service" id="debug.stopwatch"/>
      <argument type="service" id="logger" on-invalid="null"/>
      <argument type="service" id="request_stack" on-invalid="null"/>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="App\Middleware\AuthMiddleware"/>
          <argument>handle</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>console.command</argument>
        <argument type="collection">
          <argument type="service_closure" id="doctrine_migrations.schema_filter_listener"/>
          <argument>onConsoleCommand</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.response</argument>
        <argument type="collection">
          <argument type="service_closure" id="response_listener"/>
          <argument>onKernelResponse</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="locale_listener"/>
          <argument>setDefaultLocale</argument>
        </argument>
        <argument>100</argument>
      </call>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="locale_listener"/>
          <argument>onKernelRequest</argument>
        </argument>
        <argument>16</argument>
      </call>
      <call method="addListener">
        <argument>kernel.finish_request</argument>
        <argument type="collection">
          <argument type="service_closure" id="locale_listener"/>
          <argument>onKernelFinishRequest</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="validate_request_listener"/>
          <argument>onKernelRequest</argument>
        </argument>
        <argument>256</argument>
      </call>
      <call method="addListener">
        <argument>kernel.response</argument>
        <argument type="collection">
          <argument type="service_closure" id="disallow_search_engine_index_response_listener"/>
          <argument>onResponse</argument>
        </argument>
        <argument>-255</argument>
      </call>
      <call method="addListener">
        <argument>kernel.controller_arguments</argument>
        <argument type="collection">
          <argument type="service_closure" id="exception_listener"/>
          <argument>onControllerArguments</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.exception</argument>
        <argument type="collection">
          <argument type="service_closure" id="exception_listener"/>
          <argument>logKernelException</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.exception</argument>
        <argument type="collection">
          <argument type="service_closure" id="exception_listener"/>
          <argument>onKernelException</argument>
        </argument>
        <argument>-128</argument>
      </call>
      <call method="addListener">
        <argument>kernel.response</argument>
        <argument type="collection">
          <argument type="service_closure" id="exception_listener"/>
          <argument>removeCspHeader</argument>
        </argument>
        <argument>-128</argument>
      </call>
      <call method="addListener">
        <argument>kernel.controller_arguments</argument>
        <argument type="collection">
          <argument type="service_closure" id="controller.cache_attribute_listener"/>
          <argument>onKernelControllerArguments</argument>
        </argument>
        <argument>10</argument>
      </call>
      <call method="addListener">
        <argument>kernel.response</argument>
        <argument type="collection">
          <argument type="service_closure" id="controller.cache_attribute_listener"/>
          <argument>onKernelResponse</argument>
        </argument>
        <argument>-10</argument>
      </call>
      <call method="addListener">
        <argument>console.error</argument>
        <argument type="collection">
          <argument type="service_closure" id="console.error_listener"/>
          <argument>onConsoleError</argument>
        </argument>
        <argument>-128</argument>
      </call>
      <call method="addListener">
        <argument>console.terminate</argument>
        <argument type="collection">
          <argument type="service_closure" id="console.error_listener"/>
          <argument>onConsoleTerminate</argument>
        </argument>
        <argument>-128</argument>
      </call>
      <call method="addListener">
        <argument>console.error</argument>
        <argument type="collection">
          <argument type="service_closure" id="console.suggest_missing_package_subscriber"/>
          <argument>onConsoleError</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="debug.debug_handlers_listener"/>
          <argument>configure</argument>
        </argument>
        <argument>2048</argument>
      </call>
      <call method="addListener">
        <argument>console.command</argument>
        <argument type="collection">
          <argument type="service_closure" id="debug.debug_handlers_listener"/>
          <argument>configure</argument>
        </argument>
        <argument>2048</argument>
      </call>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="router_listener"/>
          <argument>onKernelRequest</argument>
        </argument>
        <argument>32</argument>
      </call>
      <call method="addListener">
        <argument>kernel.finish_request</argument>
        <argument type="collection">
          <argument type="service_closure" id="router_listener"/>
          <argument>onKernelFinishRequest</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.exception</argument>
        <argument type="collection">
          <argument type="service_closure" id="router_listener"/>
          <argument>onKernelException</argument>
        </argument>
        <argument>-64</argument>
      </call>
      <call method="addListener">
        <argument>kernel.request</argument>
        <argument type="collection">
          <argument type="service_closure" id="doctrine.dbal.idle_connection_listener"/>
          <argument>onKernelRequest</argument>
        </argument>
        <argument>192</argument>
      </call>
      <call method="addListener">
        <argument>kernel.controller</argument>
        <argument type="collection">
          <argument type="service_closure" id="sensio_framework_extra.controller.listener"/>
          <argument>onKernelController</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.controller</argument>
        <argument type="collection">
          <argument type="service_closure" id="sensio_framework_extra.converter.listener"/>
          <argument>onKernelController</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.controller</argument>
        <argument type="collection">
          <argument type="service_closure" id="sensio_framework_extra.cache.listener"/>
          <argument>onKernelController</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.response</argument>
        <argument type="collection">
          <argument type="service_closure" id="sensio_framework_extra.cache.listener"/>
          <argument>onKernelResponse</argument>
        </argument>
        <argument>0</argument>
      </call>
      <call method="addListener">
        <argument>kernel.controller_arguments</argument>
        <argument type="collection">
          <argument type="service_closure" id="framework_extra_bundle.event.is_granted"/>
          <argument>onKernelControllerArguments</argument>
        </argument>
        <argument>0</argument>
      </call>
    </service>
    <service id="debug.controller_resolver" class="Symfony\Component\HttpKernel\Controller\TraceableControllerResolver">
      <tag name="monolog.logger" channel="request"/>
      <tag name="container.decorator" id="controller_resolver" inner="debug.controller_resolver.inner"/>
      <argument type="service" id="debug.controller_resolver.inner"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id="debug.argument_resolver" class="Symfony\Component\HttpKernel\Controller\TraceableArgumentResolver">
      <tag name="container.decorator" id="argument_resolver" inner="debug.argument_resolver.inner"/>
      <argument type="service" id="debug.argument_resolver.inner"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id="argument_resolver.not_tagged_controller" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\NotTaggedControllerValueResolver">
      <tag name="controller.argument_value_resolver" priority="-200"/>
      <argument type="service" id=".service_locator.2JZTdhn"/>
    </service>
    <service id="routing.resolver" class="Symfony\Component\Config\Loader\LoaderResolver">
      <call method="addLoader">
        <argument type="service" id="routing.loader.xml"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.yml"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.php"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.glob"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.directory"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.container"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="sensio_framework_extra.routing.loader.annot_class"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="sensio_framework_extra.routing.loader.annot_dir"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="sensio_framework_extra.routing.loader.annot_file"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.attribute"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.attribute.directory"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.attribute.file"/>
      </call>
      <call method="addLoader">
        <argument type="service" id="routing.loader.psr4"/>
      </call>
    </service>
    <service id="routing.loader.xml" class="Symfony\Component\Routing\Loader\XmlFileLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.yml" class="Symfony\Component\Routing\Loader\YamlFileLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.php" class="Symfony\Component\Routing\Loader\PhpFileLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.glob" class="Symfony\Component\Routing\Loader\GlobFileLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.directory" class="Symfony\Component\Routing\Loader\DirectoryLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.container" class="Symfony\Component\Routing\Loader\ContainerLoader">
      <tag name="routing.loader"/>
      <argument type="service" id=".service_locator..6hZeC_"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.attribute" class="Symfony\Bundle\FrameworkBundle\Routing\AttributeRouteControllerLoader">
      <tag name="routing.loader" priority="-10"/>
      <argument>dev</argument>
    </service>
    <service id="routing.loader.attribute.directory" class="Symfony\Component\Routing\Loader\AttributeDirectoryLoader">
      <tag name="routing.loader" priority="-10"/>
      <argument type="service" id="file_locator"/>
      <argument type="service" id="routing.loader.attribute"/>
    </service>
    <service id="routing.loader.attribute.file" class="Symfony\Component\Routing\Loader\AttributeFileLoader">
      <tag name="routing.loader" priority="-10"/>
      <argument type="service" id="file_locator"/>
      <argument type="service" id="routing.loader.attribute"/>
    </service>
    <service id="routing.loader.psr4" class="Symfony\Component\Routing\Loader\Psr4DirectoryLoader">
      <tag name="routing.loader" priority="-10"/>
      <argument type="service" id="file_locator"/>
    </service>
    <service id="routing.loader" class="Symfony\Bundle\FrameworkBundle\Routing\DelegatingLoader" public="true">
      <argument type="service" id="routing.resolver"/>
      <argument type="collection">
        <argument key="utf8">true</argument>
      </argument>
      <argument type="collection"/>
    </service>
    <service id="router.default" class="Symfony\Bundle\FrameworkBundle\Routing\Router">
      <tag name="monolog.logger" channel="router"/>
      <tag name="container.service_subscriber" id="routing.loader"/>
      <argument type="service" id=".service_locator.PvoQzFT.router.default"/>
      <argument>kernel::loadRoutes</argument>
      <argument type="collection">
        <argument key="cache_dir">/var/www/var/cache/dev</argument>
        <argument key="debug">true</argument>
        <argument key="generator_class">Symfony\Component\Routing\Generator\CompiledUrlGenerator</argument>
        <argument key="generator_dumper_class">Symfony\Component\Routing\Generator\Dumper\CompiledUrlGeneratorDumper</argument>
        <argument key="matcher_class">Symfony\Bundle\FrameworkBundle\Routing\RedirectableCompiledUrlMatcher</argument>
        <argument key="matcher_dumper_class">Symfony\Component\Routing\Matcher\Dumper\CompiledUrlMatcherDumper</argument>
        <argument key="strict_requirements">true</argument>
        <argument key="resource_type">service</argument>
      </argument>
      <argument type="service" id="router.request_context" on-invalid="ignore"/>
      <argument type="service" id="parameter_bag" on-invalid="ignore"/>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <argument>en</argument>
      <call method="setConfigCacheFactory">
        <argument type="service" id="config_cache_factory"/>
      </call>
    </service>
    <service id="router.request_context" class="Symfony\Component\Routing\RequestContext" constructor="fromUri">
      <argument></argument>
      <argument>localhost</argument>
      <argument>http</argument>
      <argument>80</argument>
      <argument>443</argument>
    </service>
    <service id="router.cache_warmer" class="Symfony\Bundle\FrameworkBundle\CacheWarmer\RouterCacheWarmer">
      <tag name="container.service_subscriber" id="router"/>
      <tag name="kernel.cache_warmer"/>
      <argument type="service" id=".service_locator.cUcW89y.router.cache_warmer"/>
    </service>
    <service id="router_listener" class="Symfony\Component\HttpKernel\EventListener\RouterListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="monolog.logger" channel="request"/>
      <tag name="container.hot_path"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="router.default"/>
      <argument type="service" id="request_stack"/>
      <argument type="service" id="router.request_context" on-invalid="ignore"/>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <argument>/var/www</argument>
      <argument>true</argument>
    </service>
    <service id="Symfony\Bundle\FrameworkBundle\Controller\RedirectController" class="Symfony\Bundle\FrameworkBundle\Controller\RedirectController" public="true">
      <argument type="service" id="router.default"/>
      <argument type="service">
        <service class="int">
          <factory service="router.request_context" method="getHttpPort"/>
        </service>
      </argument>
      <argument type="service">
        <service class="int">
          <factory service="router.request_context" method="getHttpsPort"/>
        </service>
      </argument>
    </service>
    <service id="Symfony\Bundle\FrameworkBundle\Controller\TemplateController" class="Symfony\Bundle\FrameworkBundle\Controller\TemplateController" public="true">
      <argument>null</argument>
    </service>
    <service id="annotations.reader" class="Doctrine\Common\Annotations\AnnotationReader">
      <call method="addGlobalIgnoredName">
        <argument>required</argument>
      </call>
      <deprecated package="symfony/framework-bundle" version="6.4">The "%service_id%" service is deprecated without replacement.</deprecated>
    </service>
    <service id="annotations.cached_reader" class="Doctrine\Common\Annotations\PsrCachedReader">
      <tag name="annotations.cached_reader"/>
      <tag name="container.do_not_inline"/>
      <argument type="service" id="annotations.reader"/>
      <argument type="service">
        <service class="Symfony\Component\Cache\Adapter\ArrayAdapter"/>
      </argument>
      <argument>true</argument>
      <argument type="service_closure" id="annotations.cache_adapter"/>
      <deprecated package="symfony/framework-bundle" version="6.4">The "%service_id%" service is deprecated without replacement.</deprecated>
    </service>
    <service id="annotations.filesystem_cache_adapter" class="Symfony\Component\Cache\Adapter\FilesystemAdapter">
      <argument></argument>
      <argument>0</argument>
      <argument type="abstract">Cache-Directory</argument>
      <deprecated package="symfony/framework-bundle" version="6.4">The "%service_id%" service is deprecated without replacement.</deprecated>
    </service>
    <service id="annotations.cache_warmer" class="Symfony\Bundle\FrameworkBundle\CacheWarmer\AnnotationsCacheWarmer">
      <tag name="kernel.cache_warmer"/>
      <argument type="service" id="annotations.reader"/>
      <argument>/var/www/var/cache/dev/annotations.php</argument>
      <argument>#^Symfony\\(?:Component\\HttpKernel\\|Bundle\\FrameworkBundle\\Controller\\(?!.*Controller$))#</argument>
      <argument>true</argument>
      <argument>false</argument>
      <deprecated package="symfony/framework-bundle" version="6.4">The "%service_id%" service is deprecated without replacement.</deprecated>
    </service>
    <service id="annotations.cache_adapter" class="Symfony\Component\Cache\Adapter\PhpArrayAdapter" constructor="create">
      <tag name="container.hot_path"/>
      <argument>/var/www/var/cache/dev/annotations.php</argument>
      <argument type="service" id="cache.annotations"/>
      <deprecated package="symfony/framework-bundle" version="6.4">The "%service_id%" service is deprecated without replacement.</deprecated>
    </service>
    <service id="property_accessor" class="Symfony\Component\PropertyAccess\PropertyAccessor">
      <argument>3</argument>
      <argument>2</argument>
      <argument type="service" id="cache.property_access" on-invalid="ignore"/>
      <argument type="service" id="property_info.reflection_extractor" on-invalid="null"/>
      <argument type="service" id="property_info.reflection_extractor" on-invalid="null"/>
    </service>
    <service id="secrets.vault" class="Symfony\Bundle\FrameworkBundle\Secrets\SodiumVault">
      <tag name="container.env_var_loader"/>
      <argument>/var/www/config/secrets/%env(default:kernel.environment:APP_RUNTIME_ENV)%</argument>
      <argument type="service" id="secrets.decryption_key" on-invalid="ignore"/>
    </service>
    <service id="secrets.decryption_key" class="Symfony\Component\String\LazyString" constructor="fromCallable">
      <argument type="service" id="container.getenv"/>
      <argument>base64:default::SYMFONY_DECRYPTION_SECRET</argument>
    </service>
    <service id="secrets.local_vault" class="Symfony\Bundle\FrameworkBundle\Secrets\DotenvVault">
      <argument>/var/www/.env.dev.local</argument>
    </service>
    <service id="property_info" class="Symfony\Component\PropertyInfo\PropertyInfoExtractor">
      <argument type="iterator">
        <argument type="service" id="property_info.reflection_extractor"/>
        <argument type="service" id="doctrine.orm.default_entity_manager.property_info_extractor"/>
      </argument>
      <argument type="iterator">
        <argument type="service" id="doctrine.orm.default_entity_manager.property_info_extractor"/>
        <argument type="service" id="property_info.reflection_extractor"/>
      </argument>
      <argument type="iterator"/>
      <argument type="iterator">
        <argument type="service" id="doctrine.orm.default_entity_manager.property_info_extractor"/>
        <argument type="service" id="property_info.reflection_extractor"/>
      </argument>
      <argument type="iterator">
        <argument type="service" id="property_info.reflection_extractor"/>
      </argument>
    </service>
    <service id="property_info.reflection_extractor" class="Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor">
      <tag name="property_info.list_extractor" priority="-1000"/>
      <tag name="property_info.type_extractor" priority="-1002"/>
      <tag name="property_info.access_extractor" priority="-1000"/>
      <tag name="property_info.initializable_extractor" priority="-1000"/>
    </service>
    <service id=".cache_connection.GD_MSZC" class="Symfony\Component\Cache\Adapter\AbstractAdapter" constructor="createConnection">
      <argument>redis://localhost</argument>
      <argument type="collection">
        <argument key="lazy">true</argument>
      </argument>
    </service>
    <service id=".cache_connection.JKE6keX" class="Symfony\Component\Cache\Adapter\AbstractAdapter" constructor="createConnection">
      <argument>memcached://localhost</argument>
      <argument type="collection">
        <argument key="lazy">true</argument>
      </argument>
    </service>
    <service id="cache.property_access" class="Symfony\Component\Cache\Adapter\ArrayAdapter">
      <argument>0</argument>
      <argument>false</argument>
    </service>
    <service id="data_collector.doctrine" class="Doctrine\Bundle\DoctrineBundle\DataCollector\DoctrineDataCollector">
      <tag name="data_collector" template="@Doctrine/Collector/db.html.twig" id="db" priority="250"/>
      <argument type="service" id="doctrine"/>
      <argument>true</argument>
      <argument type="service" id="doctrine.debug_data_holder" on-invalid="null"/>
    </service>
    <service id="doctrine.dbal.connection_factory" class="Doctrine\Bundle\DoctrineBundle\ConnectionFactory">
      <argument>%doctrine.dbal.connection_factory.types%</argument>
      <argument type="service" id="doctrine.dbal.connection_factory.dsn_parser"/>
    </service>
    <service id="doctrine.dbal.connection_factory.dsn_parser" class="Doctrine\DBAL\Tools\DsnParser">
      <argument type="collection">
        <argument key="db2">ibm_db2</argument>
        <argument key="mssql">pdo_sqlsrv</argument>
        <argument key="mysql">pdo_mysql</argument>
        <argument key="mysql2">pdo_mysql</argument>
        <argument key="postgres">pdo_pgsql</argument>
        <argument key="postgresql">pdo_pgsql</argument>
        <argument key="pgsql">pdo_pgsql</argument>
        <argument key="sqlite">pdo_sqlite</argument>
        <argument key="sqlite3">pdo_sqlite</argument>
      </argument>
    </service>
    <service id="doctrine.dbal.connection" class="Doctrine\DBAL\Connection" abstract="true">
      <factory service="doctrine.dbal.connection_factory" method="createConnection"/>
    </service>
    <service id="doctrine.dbal.connection.event_manager" class="Symfony\Bridge\Doctrine\ContainerAwareEventManager" abstract="true">
      <argument type="service" id="service_container"/>
    </service>
    <service id="doctrine.dbal.connection.configuration" class="Doctrine\DBAL\Configuration" abstract="true"/>
    <service id="doctrine" class="Doctrine\Bundle\DoctrineBundle\Registry" public="true">
      <tag name="kernel.reset" method="reset"/>
      <argument type="service" id="service_container"/>
      <argument>%doctrine.connections%</argument>
      <argument>%doctrine.entity_managers%</argument>
      <argument>default</argument>
      <argument>default</argument>
    </service>
    <service id="doctrine.twig.doctrine_extension" class="Doctrine\Bundle\DoctrineBundle\Twig\DoctrineExtension">
      <tag name="twig.extension"/>
    </service>
    <service id="doctrine.dbal.schema_asset_filter_manager" class="Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager" abstract="true"/>
    <service id="doctrine.dbal.well_known_schema_asset_filter" class="Doctrine\Bundle\DoctrineBundle\Dbal\BlacklistSchemaAssetFilter">
      <argument type="collection"/>
    </service>
    <service id="doctrine.database_create_command" class="Doctrine\Bundle\DoctrineBundle\Command\CreateDatabaseDoctrineCommand">
      <tag name="console.command" command="doctrine:database:create"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine"/>
      <call method="setName">
        <argument>doctrine:database:create</argument>
      </call>
    </service>
    <service id="doctrine.database_drop_command" class="Doctrine\Bundle\DoctrineBundle\Command\DropDatabaseDoctrineCommand">
      <tag name="console.command" command="doctrine:database:drop"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine"/>
      <call method="setName">
        <argument>doctrine:database:drop</argument>
      </call>
    </service>
    <service id="doctrine.query_sql_command" class="Doctrine\Bundle\DoctrineBundle\Command\Proxy\RunSqlDoctrineCommand">
      <tag name="console.command" command="doctrine:query:sql"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="Doctrine\Bundle\DoctrineBundle\Dbal\ManagerRegistryAwareConnectionProvider" on-invalid="null"/>
      <call method="setName">
        <argument>doctrine:query:sql</argument>
      </call>
    </service>
    <service id="Doctrine\DBAL\Tools\Console\Command\RunSqlCommand" class="Doctrine\DBAL\Tools\Console\Command\RunSqlCommand">
      <tag name="console.command" command="dbal:run-sql"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="Doctrine\Bundle\DoctrineBundle\Dbal\ManagerRegistryAwareConnectionProvider" on-invalid="null"/>
      <call method="setName">
        <argument>dbal:run-sql</argument>
      </call>
    </service>
    <service id="doctrine.dbal.idle_connection_listener" class="Symfony\Bridge\Doctrine\Middleware\IdleConnection\Listener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="doctrine.dbal.connection_expiries"/>
      <argument type="service" id="service_container"/>
    </service>
    <service id="doctrine.dbal.default_schema_manager_factory" class="Doctrine\DBAL\Schema\DefaultSchemaManagerFactory"/>
    <service id="doctrine.dbal.legacy_schema_manager_factory" class="Doctrine\DBAL\Schema\LegacySchemaManagerFactory"/>
    <service id="doctrine.dbal.default_connection.configuration" class="Doctrine\DBAL\Configuration">
      <call method="setSchemaManagerFactory">
        <argument type="service" id="doctrine.dbal.legacy_schema_manager_factory"/>
      </call>
      <call method="setSchemaAssetsFilter">
        <argument type="service" id="doctrine.dbal.default_schema_asset_filter_manager"/>
      </call>
      <call method="setMiddlewares">
        <argument type="collection">
          <argument type="service" id="doctrine.dbal.debug_middleware.default"/>
          <argument type="service" id="doctrine.dbal.idle_connection_middleware.default"/>
        </argument>
      </call>
    </service>
    <service id="doctrine.dbal.default_connection.event_manager" class="Symfony\Bridge\Doctrine\ContainerAwareEventManager">
      <argument type="service" id=".service_locator.m4yXcy."/>
      <argument type="collection">
        <argument type="collection">
          <argument type="collection">
            <argument>postGenerateSchema</argument>
          </argument>
          <argument>doctrine.orm.listeners.doctrine_dbal_cache_adapter_schema_listener</argument>
        </argument>
        <argument type="collection">
          <argument type="collection">
            <argument>postGenerateSchema</argument>
          </argument>
          <argument>doctrine.orm.listeners.doctrine_token_provider_schema_listener</argument>
        </argument>
        <argument type="collection">
          <argument type="collection">
            <argument>postGenerateSchema</argument>
          </argument>
          <argument>doctrine.orm.listeners.lock_store_schema_listener</argument>
        </argument>
        <argument type="collection">
          <argument type="collection">
            <argument>loadClassMetadata</argument>
          </argument>
          <argument>doctrine.orm.default_listeners.attach_entity_listeners</argument>
        </argument>
      </argument>
    </service>
    <service id="doctrine.dbal.default_connection" class="Doctrine\DBAL\Connection" public="true">
      <argument type="collection">
        <argument key="url">**********************************/app?serverVersion=13&amp;charset=utf8</argument>
        <argument key="driver">pdo_mysql</argument>
        <argument key="idle_connection_ttl">600</argument>
        <argument key="host">localhost</argument>
        <argument key="port">null</argument>
        <argument key="user">root</argument>
        <argument key="password">null</argument>
        <argument key="driverOptions" type="collection"/>
        <argument key="defaultTableOptions" type="collection"/>
      </argument>
      <argument type="service" id="doctrine.dbal.default_connection.configuration"/>
      <argument type="service" id="doctrine.dbal.default_connection.event_manager"/>
      <argument type="collection"/>
      <factory service="doctrine.dbal.connection_factory" method="createConnection"/>
    </service>
    <service id="Doctrine\Bundle\DoctrineBundle\Dbal\ManagerRegistryAwareConnectionProvider" class="Doctrine\Bundle\DoctrineBundle\Dbal\ManagerRegistryAwareConnectionProvider">
      <argument type="service">
        <service class="Doctrine\Bundle\DoctrineBundle\Registry" public="true">
          <tag name="kernel.reset" method="reset"/>
          <argument type="service" id="service_container"/>
          <argument>%doctrine.connections%</argument>
          <argument>%doctrine.entity_managers%</argument>
          <argument>default</argument>
          <argument>default</argument>
        </service>
      </argument>
    </service>
    <service id="doctrine.dbal.connection_expiries" class="ArrayObject"/>
    <service id="doctrine.debug_data_holder" class="Doctrine\Bundle\DoctrineBundle\Middleware\BacktraceDebugDataHolder">
      <tag name="kernel.reset" method="reset"/>
      <argument type="collection"/>
    </service>
    <service id="doctrine.dbal.debug_middleware" class="Doctrine\Bundle\DoctrineBundle\Middleware\DebugMiddleware" abstract="true">
      <tag name="doctrine.middleware" connection="default" priority="10"/>
      <argument type="service" id="doctrine.debug_data_holder"/>
      <argument type="service" id="debug.stopwatch" on-invalid="null"/>
    </service>
    <service id="doctrine.dbal.idle_connection_middleware" class="Doctrine\Bundle\DoctrineBundle\Middleware\IdleConnectionMiddleware" abstract="true">
      <tag name="doctrine.middleware" connection="default" priority="10"/>
      <argument type="service" id="doctrine.dbal.connection_expiries"/>
      <argument type="collection">
        <argument key="default">600</argument>
      </argument>
    </service>
    <service id=".1_ServiceLocator~z_nBBa1" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <argument type="collection"/>
    </service>
    <service id="doctrine.orm.proxy_cache_warmer" class="Symfony\Bridge\Doctrine\CacheWarmer\ProxyCacheWarmer">
      <tag name="kernel.cache_warmer"/>
      <argument type="service" id="doctrine"/>
    </service>
    <service id="form.type_guesser.doctrine" class="Symfony\Bridge\Doctrine\Form\DoctrineOrmTypeGuesser">
      <tag name="form.type_guesser"/>
      <argument type="service" id="doctrine"/>
    </service>
    <service id="form.type.entity" class="Symfony\Bridge\Doctrine\Form\Type\EntityType">
      <tag name="form.type" alias="entity"/>
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.orm.configuration" class="Doctrine\ORM\Configuration" abstract="true"/>
    <service id="doctrine.orm.entity_manager.abstract" class="Doctrine\ORM\EntityManager" lazy="true" abstract="true"/>
    <service id="doctrine.orm.container_repository_factory" class="Doctrine\Bundle\DoctrineBundle\Repository\ContainerRepositoryFactory">
      <argument type="service" id=".service_locator.Awd_APQ"/>
    </service>
    <service id="doctrine.orm.manager_configurator.abstract" class="Doctrine\Bundle\DoctrineBundle\ManagerConfigurator" abstract="true">
      <argument type="collection"/>
      <argument type="collection"/>
    </service>
    <service id="doctrine.orm.validator.unique" class="Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntityValidator">
      <tag name="validator.constraint_validator" alias="doctrine.orm.validator.unique"/>
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.orm.validator_initializer" class="Symfony\Bridge\Doctrine\Validator\DoctrineInitializer">
      <tag name="validator.initializer"/>
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.orm.security.user.provider" class="Symfony\Bridge\Doctrine\Security\User\EntityUserProvider" abstract="true">
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.orm.listeners.resolve_target_entity" class="Doctrine\ORM\Tools\ResolveTargetEntityListener"/>
    <service id="doctrine.orm.listeners.doctrine_dbal_cache_adapter_schema_listener" class="Symfony\Bridge\Doctrine\SchemaListener\DoctrineDbalCacheAdapterSchemaListener">
      <tag name="doctrine.event_listener" event="postGenerateSchema"/>
      <argument type="collection"/>
    </service>
    <service id="doctrine.orm.listeners.doctrine_token_provider_schema_listener" class="Symfony\Bridge\Doctrine\SchemaListener\RememberMeTokenProviderDoctrineSchemaListener">
      <tag name="doctrine.event_listener" event="postGenerateSchema"/>
      <argument type="tagged_iterator" tag="security.remember_me_handler"/>
    </service>
    <service id="doctrine.orm.listeners.lock_store_schema_listener" class="Symfony\Bridge\Doctrine\SchemaListener\LockStoreSchemaListener">
      <tag name="doctrine.event_listener" event="postGenerateSchema"/>
      <argument type="tagged_iterator" tag="lock.store"/>
    </service>
    <service id="doctrine.orm.naming_strategy.default" class="Doctrine\ORM\Mapping\DefaultNamingStrategy"/>
    <service id="doctrine.orm.naming_strategy.underscore" class="Doctrine\ORM\Mapping\UnderscoreNamingStrategy"/>
    <service id="doctrine.orm.naming_strategy.underscore_number_aware" class="Doctrine\ORM\Mapping\UnderscoreNamingStrategy">
      <argument>0</argument>
      <argument>true</argument>
    </service>
    <service id="doctrine.orm.quote_strategy.default" class="Doctrine\ORM\Mapping\DefaultQuoteStrategy"/>
    <service id="doctrine.orm.quote_strategy.ansi" class="Doctrine\ORM\Mapping\AnsiQuoteStrategy"/>
    <service id="doctrine.orm.typed_field_mapper.default" class="Doctrine\ORM\Mapping\DefaultTypedFieldMapper"/>
    <service id="doctrine.ulid_generator" class="Symfony\Bridge\Doctrine\IdGenerator\UlidGenerator">
      <tag name="doctrine.id_generator"/>
      <argument>null</argument>
    </service>
    <service id="doctrine.uuid_generator" class="Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator">
      <tag name="doctrine.id_generator"/>
      <argument>null</argument>
    </service>
    <service id="doctrine.orm.command.entity_manager_provider" class="Doctrine\Bundle\DoctrineBundle\Orm\ManagerRegistryAwareEntityManagerProvider">
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.orm.entity_value_resolver" class="Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver">
      <tag name="Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver" priority="110">controller.argument_value_resolver</tag>
      <argument type="service" id="doctrine"/>
      <argument>null</argument>
      <argument type="service">
        <service class="Symfony\Bridge\Doctrine\Attribute\MapEntity">
          <argument>null</argument>
          <argument>null</argument>
          <argument>null</argument>
          <argument>null</argument>
          <argument>null</argument>
          <argument>null</argument>
          <argument>null</argument>
          <argument>null</argument>
          <argument>false</argument>
        </service>
      </argument>
      <argument type="collection"/>
    </service>
    <service id="doctrine.cache_clear_metadata_command" class="Doctrine\ORM\Tools\Console\Command\ClearCache\MetadataCommand">
      <tag name="console.command" command="doctrine:cache:clear-metadata"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:cache:clear-metadata</argument>
      </call>
    </service>
    <service id="doctrine.cache_clear_query_cache_command" class="Doctrine\ORM\Tools\Console\Command\ClearCache\QueryCommand">
      <tag name="console.command" command="doctrine:cache:clear-query"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:cache:clear-query</argument>
      </call>
    </service>
    <service id="doctrine.cache_clear_result_command" class="Doctrine\ORM\Tools\Console\Command\ClearCache\ResultCommand">
      <tag name="console.command" command="doctrine:cache:clear-result"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:cache:clear-result</argument>
      </call>
    </service>
    <service id="doctrine.cache_collection_region_command" class="Doctrine\ORM\Tools\Console\Command\ClearCache\CollectionRegionCommand">
      <tag name="console.command" command="doctrine:cache:clear-collection-region"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:cache:clear-collection-region</argument>
      </call>
    </service>
    <service id="doctrine.mapping_convert_command" class="Doctrine\ORM\Tools\Console\Command\ConvertMappingCommand">
      <tag name="console.command" command="doctrine:mapping:convert"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:mapping:convert</argument>
      </call>
    </service>
    <service id="doctrine.schema_create_command" class="Doctrine\ORM\Tools\Console\Command\SchemaTool\CreateCommand">
      <tag name="console.command" command="doctrine:schema:create"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:schema:create</argument>
      </call>
    </service>
    <service id="doctrine.schema_drop_command" class="Doctrine\ORM\Tools\Console\Command\SchemaTool\DropCommand">
      <tag name="console.command" command="doctrine:schema:drop"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:schema:drop</argument>
      </call>
    </service>
    <service id="doctrine.ensure_production_settings_command" class="Doctrine\ORM\Tools\Console\Command\EnsureProductionSettingsCommand">
      <tag name="console.command" command="doctrine:ensure-production-settings"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:ensure-production-settings</argument>
      </call>
    </service>
    <service id="doctrine.clear_entity_region_command" class="Doctrine\ORM\Tools\Console\Command\ClearCache\EntityRegionCommand">
      <tag name="console.command" command="doctrine:cache:clear-entity-region"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:cache:clear-entity-region</argument>
      </call>
    </service>
    <service id="doctrine.mapping_info_command" class="Doctrine\ORM\Tools\Console\Command\InfoCommand">
      <tag name="console.command" command="doctrine:mapping:info"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:mapping:info</argument>
      </call>
    </service>
    <service id="doctrine.clear_query_region_command" class="Doctrine\ORM\Tools\Console\Command\ClearCache\QueryRegionCommand">
      <tag name="console.command" command="doctrine:cache:clear-query-region"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:cache:clear-query-region</argument>
      </call>
    </service>
    <service id="doctrine.query_dql_command" class="Doctrine\ORM\Tools\Console\Command\RunDqlCommand">
      <tag name="console.command" command="doctrine:query:dql"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:query:dql</argument>
      </call>
    </service>
    <service id="doctrine.schema_update_command" class="Doctrine\ORM\Tools\Console\Command\SchemaTool\UpdateCommand">
      <tag name="console.command" command="doctrine:schema:update"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:schema:update</argument>
      </call>
    </service>
    <service id="doctrine.schema_validate_command" class="Doctrine\ORM\Tools\Console\Command\ValidateSchemaCommand">
      <tag name="console.command" command="doctrine:schema:validate"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.orm.command.entity_manager_provider"/>
      <call method="setName">
        <argument>doctrine:schema:validate</argument>
      </call>
    </service>
    <service id="doctrine.mapping_import_command" class="Doctrine\Bundle\DoctrineBundle\Command\ImportMappingDoctrineCommand">
      <tag name="console.command" command="doctrine:mapping:import"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine"/>
      <argument>%kernel.bundles%</argument>
      <call method="setName">
        <argument>doctrine:mapping:import</argument>
      </call>
    </service>
    <service id="doctrine.orm.default_configuration" class="Doctrine\ORM\Configuration">
      <tag name="doctrine.orm.configuration"/>
      <call method="setEntityNamespaces">
        <argument type="collection">
          <argument key="App">App\Entity</argument>
        </argument>
      </call>
      <call method="setMetadataCache">
        <argument type="service" id="cache.doctrine.orm.default.metadata"/>
      </call>
      <call method="setQueryCache">
        <argument type="service" id="cache.doctrine.orm.default.query"/>
      </call>
      <call method="setResultCache">
        <argument type="service" id="cache.doctrine.orm.default.result"/>
      </call>
      <call method="setMetadataDriverImpl">
        <argument type="service" id=".doctrine.orm.default_metadata_driver"/>
      </call>
      <call method="setProxyDir">
        <argument>/var/www/var/cache/dev/doctrine/orm/Proxies</argument>
      </call>
      <call method="setProxyNamespace">
        <argument>Proxies</argument>
      </call>
      <call method="setAutoGenerateProxyClasses">
        <argument>true</argument>
      </call>
      <call method="setSchemaIgnoreClasses">
        <argument type="collection"/>
      </call>
      <call method="setClassMetadataFactoryName">
        <argument>Doctrine\Bundle\DoctrineBundle\Mapping\ClassMetadataFactory</argument>
      </call>
      <call method="setDefaultRepositoryClassName">
        <argument>Doctrine\ORM\EntityRepository</argument>
      </call>
      <call method="setNamingStrategy">
        <argument type="service" id="doctrine.orm.naming_strategy.underscore_number_aware"/>
      </call>
      <call method="setQuoteStrategy">
        <argument type="service" id="doctrine.orm.quote_strategy.default"/>
      </call>
      <call method="setTypedFieldMapper">
        <argument type="service" id="doctrine.orm.typed_field_mapper.default"/>
      </call>
      <call method="setEntityListenerResolver">
        <argument type="service" id="doctrine.orm.default_entity_listener_resolver"/>
      </call>
      <call method="setLazyGhostObjectEnabled">
        <argument>false</argument>
      </call>
      <call method="setIdentityGenerationPreferences">
        <argument type="collection"/>
      </call>
      <call method="setRepositoryFactory">
        <argument type="service" id="doctrine.orm.container_repository_factory"/>
      </call>
    </service>
    <service id="doctrine.orm.default_attribute_metadata_driver" class="Doctrine\ORM\Mapping\Driver\AttributeDriver">
      <argument type="collection">
        <argument>/var/www/src/Entity</argument>
      </argument>
      <argument>false</argument>
    </service>
    <service id="cache.doctrine.orm.default.metadata" class="Symfony\Component\Cache\Adapter\ArrayAdapter"/>
    <service id="cache.doctrine.orm.default.result" class="Symfony\Component\Cache\Adapter\ArrayAdapter">
      <tag name="cache.pool"/>
    </service>
    <service id="cache.doctrine.orm.default.query" class="Symfony\Component\Cache\Adapter\ArrayAdapter">
      <tag name="cache.pool"/>
    </service>
    <service id="doctrine.orm.default_entity_listener_resolver" class="Doctrine\Bundle\DoctrineBundle\Mapping\ContainerEntityListenerResolver">
      <argument type="service" id="service_container"/>
    </service>
    <service id="doctrine.orm.default_listeners.attach_entity_listeners" class="Doctrine\ORM\Tools\AttachEntityListenersListener">
      <tag name="doctrine.event_listener" event="loadClassMetadata"/>
    </service>
    <service id="doctrine.orm.default_manager_configurator" class="Doctrine\Bundle\DoctrineBundle\ManagerConfigurator">
      <argument type="collection"/>
      <argument type="collection"/>
    </service>
    <service id="doctrine.orm.default_entity_manager" class="Doctrine\ORM\EntityManager" public="true" lazy="true">
      <tag name="container.preload" class="Doctrine\ORM\Proxy\Autoloader"/>
      <argument type="service" id="doctrine.dbal.default_connection"/>
      <argument type="service" id="doctrine.orm.default_configuration"/>
      <argument type="service" id="doctrine.dbal.default_connection.event_manager"/>
      <configurator service="doctrine.orm.default_manager_configurator" method="configure"/>
    </service>
    <service id="doctrine.orm.default_entity_manager.property_info_extractor" class="Symfony\Bridge\Doctrine\PropertyInfo\DoctrineExtractor">
      <tag name="property_info.list_extractor" priority="-1001"/>
      <tag name="property_info.type_extractor" priority="-999"/>
      <tag name="property_info.access_extractor" priority="-999"/>
      <argument type="service" id="doctrine.orm.default_entity_manager"/>
    </service>
    <service id="doctrine.migrations.dependency_factory" class="Doctrine\Migrations\DependencyFactory" constructor="fromEntityManager">
      <argument type="service" id="doctrine.migrations.configuration_loader"/>
      <argument type="service" id="doctrine.migrations.entity_manager_registry_loader"/>
      <argument type="service" id="logger" on-invalid="null"/>
      <call method="setDefinition">
        <argument>Doctrine\Migrations\Version\MigrationFactory</argument>
        <argument type="service_closure" id="doctrine.migrations.container_aware_migrations_factory"/>
      </call>
    </service>
    <service id="doctrine.migrations.configuration_loader" class="Doctrine\Migrations\Configuration\Migration\ExistingConfiguration">
      <argument type="service" id="doctrine.migrations.configuration"/>
    </service>
    <service id="doctrine.migrations.connection_loader" class="Doctrine\Migrations\Configuration\Connection\ExistingConnection"/>
    <service id="doctrine.migrations.em_loader" class="Doctrine\Migrations\Configuration\EntityManager\ExistingEntityManager"/>
    <service id="doctrine.migrations.entity_manager_registry_loader" class="Doctrine\Migrations\Configuration\EntityManager\ManagerRegistryEntityManager" constructor="withSimpleDefault">
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.migrations.connection_registry_loader" class="Doctrine\Migrations\Configuration\Connection\ConnectionRegistryConnection" constructor="withSimpleDefault">
      <argument type="service" id="doctrine"/>
    </service>
    <service id="doctrine.migrations.configuration" class="Doctrine\Migrations\Configuration\Configuration">
      <call method="addMigrationsDirectory">
        <argument>App\Migrations</argument>
        <argument>/var/www/migrations</argument>
      </call>
      <call method="setAllOrNothing">
        <argument>false</argument>
      </call>
      <call method="setCheckDatabasePlatform">
        <argument>true</argument>
      </call>
      <call method="setTransactional">
        <argument>true</argument>
      </call>
      <call method="setMetadataStorageConfiguration">
        <argument type="service" id="doctrine.migrations.storage.table_storage"/>
      </call>
    </service>
    <service id="doctrine.migrations.container_aware_migrations_factory" class="Doctrine\Bundle\MigrationsBundle\MigrationsFactory\ContainerAwareMigrationFactory">
      <tag name="container.decorator" id="doctrine.migrations.migrations_factory" inner="doctrine.migrations.container_aware_migrations_factory.inner"/>
      <argument type="service" id="doctrine.migrations.container_aware_migrations_factory.inner"/>
      <argument type="service" id="service_container"/>
    </service>
    <service id="doctrine_migrations.diff_command" class="Doctrine\Migrations\Tools\Console\Command\DiffCommand">
      <tag name="console.command" command="doctrine:migrations:diff"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:diff</argument>
      <call method="setName">
        <argument>doctrine:migrations:diff</argument>
      </call>
      <call method="setDescription">
        <argument>Generate a migration by comparing your current database to your mapping information.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.sync_metadata_command" class="Doctrine\Migrations\Tools\Console\Command\SyncMetadataCommand">
      <tag name="console.command" command="doctrine:migrations:sync-metadata-storage"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:sync-metadata-storage</argument>
      <call method="setName">
        <argument>doctrine:migrations:sync-metadata-storage</argument>
      </call>
      <call method="setDescription">
        <argument>Ensures that the metadata storage is at the latest version.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.versions_command" class="Doctrine\Migrations\Tools\Console\Command\ListCommand">
      <tag name="console.command" command="doctrine:migrations:list"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:versions</argument>
      <call method="setName">
        <argument>doctrine:migrations:list</argument>
      </call>
      <call method="setDescription">
        <argument>Display a list of all available migrations and their status.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.current_command" class="Doctrine\Migrations\Tools\Console\Command\CurrentCommand">
      <tag name="console.command" command="doctrine:migrations:current"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:current</argument>
      <call method="setName">
        <argument>doctrine:migrations:current</argument>
      </call>
      <call method="setDescription">
        <argument>Outputs the current version</argument>
      </call>
    </service>
    <service id="doctrine_migrations.dump_schema_command" class="Doctrine\Migrations\Tools\Console\Command\DumpSchemaCommand">
      <tag name="console.command" command="doctrine:migrations:dump-schema"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:dump-schema</argument>
      <call method="setName">
        <argument>doctrine:migrations:dump-schema</argument>
      </call>
      <call method="setDescription">
        <argument>Dump the schema for your database to a migration.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.execute_command" class="Doctrine\Migrations\Tools\Console\Command\ExecuteCommand">
      <tag name="console.command" command="doctrine:migrations:execute"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:execute</argument>
      <call method="setName">
        <argument>doctrine:migrations:execute</argument>
      </call>
      <call method="setDescription">
        <argument>Execute one or more migration versions up or down manually.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.generate_command" class="Doctrine\Migrations\Tools\Console\Command\GenerateCommand">
      <tag name="console.command" command="doctrine:migrations:generate"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:generate</argument>
      <call method="setName">
        <argument>doctrine:migrations:generate</argument>
      </call>
      <call method="setDescription">
        <argument>Generate a blank migration class.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.latest_command" class="Doctrine\Migrations\Tools\Console\Command\LatestCommand">
      <tag name="console.command" command="doctrine:migrations:latest"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:latest</argument>
      <call method="setName">
        <argument>doctrine:migrations:latest</argument>
      </call>
      <call method="setDescription">
        <argument>Outputs the latest version</argument>
      </call>
    </service>
    <service id="doctrine_migrations.migrate_command" class="Doctrine\Migrations\Tools\Console\Command\MigrateCommand">
      <tag name="console.command" command="doctrine:migrations:migrate"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:migrate</argument>
      <call method="setName">
        <argument>doctrine:migrations:migrate</argument>
      </call>
      <call method="setDescription">
        <argument>Execute a migration to a specified version or the latest available version.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.rollup_command" class="Doctrine\Migrations\Tools\Console\Command\RollupCommand">
      <tag name="console.command" command="doctrine:migrations:rollup"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:rollup</argument>
      <call method="setName">
        <argument>doctrine:migrations:rollup</argument>
      </call>
      <call method="setDescription">
        <argument>Rollup migrations by deleting all tracked versions and insert the one version that exists.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.status_command" class="Doctrine\Migrations\Tools\Console\Command\StatusCommand">
      <tag name="console.command" command="doctrine:migrations:status"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:status</argument>
      <call method="setName">
        <argument>doctrine:migrations:status</argument>
      </call>
      <call method="setDescription">
        <argument>View the status of a set of migrations.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.up_to_date_command" class="Doctrine\Migrations\Tools\Console\Command\UpToDateCommand">
      <tag name="console.command" command="doctrine:migrations:up-to-date"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:up-to-date</argument>
      <call method="setName">
        <argument>doctrine:migrations:up-to-date</argument>
      </call>
      <call method="setDescription">
        <argument>Tells you if your schema is up-to-date.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.version_command" class="Doctrine\Migrations\Tools\Console\Command\VersionCommand">
      <tag name="console.command" command="doctrine:migrations:version"/>
      <tag name="container.no_preload"/>
      <argument type="service" id="doctrine.migrations.dependency_factory"/>
      <argument>doctrine:migrations:version</argument>
      <call method="setName">
        <argument>doctrine:migrations:version</argument>
      </call>
      <call method="setDescription">
        <argument>Manually add and delete migration versions from the version table.</argument>
      </call>
    </service>
    <service id="doctrine_migrations.schema_filter_listener" class="Doctrine\Bundle\MigrationsBundle\EventListener\SchemaFilterListener">
      <tag name="kernel.event_listener" event="console.command" method="onConsoleCommand"/>
      <tag name="doctrine.dbal.schema_filter" connection="default"/>
      <tag name="container.no_preload"/>
      <argument>doctrine_migration_versions</argument>
    </service>
    <service id="doctrine.migrations.storage.table_storage" class="Doctrine\Migrations\Metadata\Storage\TableMetadataStorageConfiguration"/>
    <service id="sensio_framework_extra.controller.listener" class="Sensio\Bundle\FrameworkExtraBundle\EventListener\ControllerListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="annotations.cached_reader"/>
    </service>
    <service id="sensio_framework_extra.routing.loader.annot_class" class="Sensio\Bundle\FrameworkExtraBundle\Routing\AnnotatedRouteControllerLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="annotations.cached_reader"/>
      <deprecated package="sensio/framework-extra-bundle" version="5.2">The "%service_id%" service is deprecated since version 5.2</deprecated>
    </service>
    <service id="sensio_framework_extra.routing.loader.annot_dir" class="Symfony\Component\Routing\Loader\AnnotationDirectoryLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument type="service" id="sensio_framework_extra.routing.loader.annot_class"/>
      <deprecated package="sensio/framework-extra-bundle" version="5.2">The "%service_id%" service is deprecated since version 5.2</deprecated>
    </service>
    <service id="sensio_framework_extra.routing.loader.annot_file" class="Symfony\Component\Routing\Loader\AnnotationFileLoader">
      <tag name="routing.loader"/>
      <argument type="service" id="file_locator"/>
      <argument type="service" id="sensio_framework_extra.routing.loader.annot_class"/>
      <deprecated package="sensio/framework-extra-bundle" version="5.2">The "%service_id%" service is deprecated since version 5.2</deprecated>
    </service>
    <service id="sensio_framework_extra.converter.listener" class="Sensio\Bundle\FrameworkExtraBundle\EventListener\ParamConverterListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="sensio_framework_extra.converter.manager"/>
      <argument>true</argument>
    </service>
    <service id="sensio_framework_extra.converter.manager" class="Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterManager">
      <call method="add">
        <argument type="service" id="sensio_framework_extra.converter.doctrine.orm"/>
        <argument>0</argument>
        <argument>doctrine.orm</argument>
      </call>
      <call method="add">
        <argument type="service" id="framework_extra_bundle.date_time_param_converter"/>
        <argument>0</argument>
        <argument>datetime</argument>
      </call>
    </service>
    <service id="sensio_framework_extra.converter.doctrine.orm" class="Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\DoctrineParamConverter">
      <tag name="request.param_converter" converter="doctrine.orm"/>
      <argument type="service" id="doctrine" on-invalid="ignore"/>
      <argument>null</argument>
    </service>
    <service id="framework_extra_bundle.date_time_param_converter" class="Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\DateTimeParamConverter">
      <tag name="request.param_converter" converter="datetime"/>
    </service>
    <service id="sensio_framework_extra.view.guesser" class="Sensio\Bundle\FrameworkExtraBundle\Templating\TemplateGuesser">
      <argument type="service" id="kernel"/>
    </service>
    <service id="sensio_framework_extra.cache.listener" class="Sensio\Bundle\FrameworkExtraBundle\EventListener\HttpCacheListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <tag name="container.hot_path"/>
    </service>
    <service id="framework_extra_bundle.event.is_granted" class="Sensio\Bundle\FrameworkExtraBundle\EventListener\IsGrantedListener">
      <tag name="kernel.event_subscriber"/>
      <tag name="container.hot_path"/>
      <argument type="service" id="framework_extra_bundle.argument_name_convertor"/>
      <argument>null</argument>
    </service>
    <service id="framework_extra_bundle.argument_name_convertor" class="Sensio\Bundle\FrameworkExtraBundle\Request\ArgumentNameConverter">
      <argument type="service" id="argument_metadata_factory"/>
    </service>
    <service id=".instanceof.Symfony\Component\Console\Command\Command.0.App\Command\FetchPricesCommand" class="App\Command\FetchPricesCommand" autowire="true" abstract="true"/>
    <service id=".abstract.instanceof.App\Command\FetchPricesCommand" class="App\Command\FetchPricesCommand" autowire="true" autoconfigure="true" abstract="true"/>
    <service id=".instanceof.Symfony\Contracts\Service\ServiceSubscriberInterface.0.App\Controller\PriceController" class="App\Controller\PriceController" autowire="true" abstract="true"/>
    <service id=".instanceof.Symfony\Bundle\FrameworkBundle\Controller\AbstractController.0.App\Controller\PriceController" class="App\Controller\PriceController" autowire="true" abstract="true"/>
    <service id=".abstract.instanceof.App\Controller\PriceController" class="App\Controller\PriceController" autowire="true" autoconfigure="true" abstract="true"/>
    <service id=".instanceof.Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepositoryInterface.0.App\Repository\ProductPriceRepository" class="App\Repository\ProductPriceRepository" autowire="true" abstract="true"/>
    <service id=".abstract.instanceof.App\Repository\ProductPriceRepository" class="App\Repository\ProductPriceRepository" autowire="true" autoconfigure="true" abstract="true"/>
    <service id=".service_locator.w7.f4fT" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="base64" type="service_closure" id="container.env_var_processor"/>
        <argument key="bool" type="service_closure" id="container.env_var_processor"/>
        <argument key="const" type="service_closure" id="container.env_var_processor"/>
        <argument key="csv" type="service_closure" id="container.env_var_processor"/>
        <argument key="default" type="service_closure" id="container.env_var_processor"/>
        <argument key="defined" type="service_closure" id="container.env_var_processor"/>
        <argument key="enum" type="service_closure" id="container.env_var_processor"/>
        <argument key="file" type="service_closure" id="container.env_var_processor"/>
        <argument key="float" type="service_closure" id="container.env_var_processor"/>
        <argument key="int" type="service_closure" id="container.env_var_processor"/>
        <argument key="json" type="service_closure" id="container.env_var_processor"/>
        <argument key="key" type="service_closure" id="container.env_var_processor"/>
        <argument key="not" type="service_closure" id="container.env_var_processor"/>
        <argument key="query_string" type="service_closure" id="container.env_var_processor"/>
        <argument key="require" type="service_closure" id="container.env_var_processor"/>
        <argument key="resolve" type="service_closure" id="container.env_var_processor"/>
        <argument key="shuffle" type="service_closure" id="container.env_var_processor"/>
        <argument key="string" type="service_closure" id="container.env_var_processor"/>
        <argument key="trim" type="service_closure" id="container.env_var_processor"/>
        <argument key="url" type="service_closure" id="container.env_var_processor"/>
      </argument>
    </service>
    <service id=".service_locator.y4_Zrx." class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="loader" type="service_closure" id=".errored..service_locator.y4_Zrx..Symfony\Component\Config\Loader\LoaderInterface"/>
      </argument>
    </service>
    <service id=".service_locator.2JZTdhn" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="kernel::loadRoutes" type="service_closure" id=".service_locator.y4_Zrx."/>
        <argument key="kernel::registerContainerConfiguration" type="service_closure" id=".service_locator.y4_Zrx."/>
        <argument key="kernel:loadRoutes" type="service_closure" id=".service_locator.y4_Zrx."/>
        <argument key="kernel:registerContainerConfiguration" type="service_closure" id=".service_locator.y4_Zrx."/>
      </argument>
    </service>
    <service id=".service_locator.lLv4pWF" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="inline" type="service_closure" id="fragment.renderer.inline"/>
      </argument>
    </service>
    <service id=".debug.value_resolver.doctrine.orm.entity_value_resolver" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="doctrine.orm.entity_value_resolver"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.backed_enum_resolver" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.backed_enum_resolver"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.datetime" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.datetime"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.request_attribute" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.request_attribute"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.request" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.request"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.session" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.session"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.service" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.service"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.default" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.default"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.variadic" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.variadic"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.not_tagged_controller" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.not_tagged_controller"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.request_payload" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.request_payload"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".debug.value_resolver.argument_resolver.query_parameter_value_resolver" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver">
      <argument type="service" id="argument_resolver.query_parameter_value_resolver"/>
      <argument type="service" id="debug.stopwatch"/>
    </service>
    <service id=".service_locator.m4yXcy." class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="doctrine.orm.default_listeners.attach_entity_listeners" type="service_closure" id="doctrine.orm.default_listeners.attach_entity_listeners"/>
        <argument key="doctrine.orm.listeners.doctrine_dbal_cache_adapter_schema_listener" type="service_closure" id="doctrine.orm.listeners.doctrine_dbal_cache_adapter_schema_listener"/>
        <argument key="doctrine.orm.listeners.doctrine_token_provider_schema_listener" type="service_closure" id="doctrine.orm.listeners.doctrine_token_provider_schema_listener"/>
        <argument key="doctrine.orm.listeners.lock_store_schema_listener" type="service_closure" id="doctrine.orm.listeners.lock_store_schema_listener"/>
      </argument>
    </service>
    <service id=".service_locator.Awd_APQ" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="App\Repository\ProductPriceRepository" type="service_closure" id="App\Repository\ProductPriceRepository"/>
      </argument>
    </service>
    <service id=".service_locator.KLVvNIq" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="doctrine.ulid_generator" type="service_closure" id="doctrine.ulid_generator"/>
        <argument key="doctrine.uuid_generator" type="service_closure" id="doctrine.uuid_generator"/>
      </argument>
    </service>
    <service id=".doctrine.orm.default_metadata_driver" class="Doctrine\Bundle\DoctrineBundle\Mapping\MappingDriver">
      <tag name="container.decorator" id="doctrine.orm.default_metadata_driver" inner=".doctrine.orm.default_metadata_driver.inner"/>
      <argument type="service" id=".doctrine.orm.default_metadata_driver.inner"/>
      <argument type="service" id=".service_locator.KLVvNIq"/>
    </service>
    <service id="doctrine.dbal.default_schema_asset_filter_manager" class="Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager">
      <argument type="collection">
        <argument type="service" id="doctrine_migrations.schema_filter_listener"/>
      </argument>
    </service>
    <service id="doctrine.dbal.debug_middleware.default" class="Doctrine\Bundle\DoctrineBundle\Middleware\DebugMiddleware">
      <argument type="service" id="doctrine.debug_data_holder"/>
      <argument type="service" id="debug.stopwatch" on-invalid="null"/>
      <call method="setConnectionName">
        <argument>default</argument>
      </call>
    </service>
    <service id="doctrine.dbal.idle_connection_middleware.default" class="Doctrine\Bundle\DoctrineBundle\Middleware\IdleConnectionMiddleware">
      <argument type="service" id="doctrine.dbal.connection_expiries"/>
      <argument type="collection">
        <argument key="default">600</argument>
      </argument>
      <call method="setConnectionName">
        <argument>default</argument>
      </call>
    </service>
    <service id="logger" class="Symfony\Component\HttpKernel\Log\Logger">
      <argument>null</argument>
      <argument>null</argument>
      <argument>null</argument>
      <argument type="service" id="request_stack"/>
      <argument>%env(bool:default::key:web:default:kernel.runtime_mode:)%</argument>
    </service>
    <service id=".service_locator.O2p6Lk7" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="http_kernel" type="service_closure" id="http_kernel" on-invalid="ignore"/>
        <argument key="parameter_bag" type="service_closure" id="parameter_bag" on-invalid="ignore"/>
        <argument key="request_stack" type="service_closure" id="request_stack" on-invalid="ignore"/>
        <argument key="router" type="service_closure" id="router.default" on-invalid="ignore"/>
      </argument>
    </service>
    <service id=".service_locator.O2p6Lk7.App\Controller\PriceController" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator_context" id="App\Controller\PriceController"/>
      <argument>App\Controller\PriceController</argument>
      <argument type="service" id="service_container"/>
      <factory service=".service_locator.O2p6Lk7" method="withContext"/>
    </service>
    <service id=".service_locator.PvoQzFT" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="routing.loader" type="service_closure" id="routing.loader"/>
      </argument>
    </service>
    <service id=".service_locator.PvoQzFT.router.default" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator_context" id="router.default"/>
      <argument>router.default</argument>
      <argument type="service" id="service_container"/>
      <factory service=".service_locator.PvoQzFT" method="withContext"/>
    </service>
    <service id=".service_locator.cUcW89y" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="router" type="service_closure" id="router.default"/>
      </argument>
    </service>
    <service id=".service_locator.cUcW89y.router.cache_warmer" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator_context" id="router.cache_warmer"/>
      <argument>router.cache_warmer</argument>
      <argument type="service" id="service_container"/>
      <factory service=".service_locator.cUcW89y" method="withContext"/>
    </service>
    <service id=".service_locator._ju.bgC" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver" type="service_closure" id=".debug.value_resolver.doctrine.orm.entity_value_resolver"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.backed_enum_resolver"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.datetime"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.default"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.query_parameter_value_resolver"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.request_attribute"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestPayloadValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.request_payload"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.request"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.service"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.session"/>
        <argument key="Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver" type="service_closure" id=".debug.value_resolver.argument_resolver.variadic"/>
        <argument key="argument_resolver.not_tagged_controller" type="service_closure" id=".debug.value_resolver.argument_resolver.not_tagged_controller"/>
      </argument>
    </service>
    <service id=".service_locator.XXv1IfR" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="reverse_container" type="service_closure" id="reverse_container" on-invalid="ignore_uninitialized"/>
      </argument>
    </service>
    <service id=".service_locator.Xbsa8iG" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection"/>
    </service>
    <service id=".service_locator.NBUFN6A" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="cache.app" type="service_closure" id="cache.app.taggable"/>
      </argument>
    </service>
    <service id=".service_locator.9ETxUxh" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="event_dispatcher" type="service_closure" id="debug.event_dispatcher"/>
      </argument>
    </service>
    <service id=".service_locator..6hZeC_" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="kernel" type="service_closure" id="kernel"/>
      </argument>
    </service>
    <service id="debug.event_dispatcher.inner" class="Symfony\Component\EventDispatcher\EventDispatcher"/>
    <service id="debug.controller_resolver.inner" class="Symfony\Bundle\FrameworkBundle\Controller\ControllerResolver">
      <argument type="service" id="service_container"/>
      <argument type="service" id="logger" on-invalid="ignore"/>
      <call method="allowControllers">
        <argument type="collection">
          <argument>Symfony\Bundle\FrameworkBundle\Controller\AbstractController</argument>
          <argument>Symfony\Bundle\FrameworkBundle\Controller\TemplateController</argument>
        </argument>
      </call>
      <call method="allowControllers">
        <argument type="collection">
          <argument>App\Kernel</argument>
          <argument>App\Controller\PriceController</argument>
          <argument>Doctrine\Bundle\DoctrineBundle\Controller\ProfilerController</argument>
        </argument>
      </call>
    </service>
    <service id="debug.argument_resolver.inner" class="Symfony\Component\HttpKernel\Controller\ArgumentResolver">
      <argument type="service" id="argument_metadata_factory"/>
      <argument type="iterator">
        <argument type="service" id=".debug.value_resolver.doctrine.orm.entity_value_resolver"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.backed_enum_resolver"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.datetime"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.request_attribute"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.request"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.session"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.service"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.default"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.variadic"/>
        <argument type="service" id=".debug.value_resolver.argument_resolver.not_tagged_controller"/>
      </argument>
      <argument type="service" id=".service_locator._ju.bgC"/>
    </service>
    <service id="doctrine.migrations.container_aware_migrations_factory.inner" class="Doctrine\Migrations\Version\MigrationFactory">
      <factory service="doctrine.migrations.dependency_factory" method="getMigrationFactory"/>
    </service>
    <service id=".doctrine.orm.default_metadata_driver.inner" class="Doctrine\Persistence\Mapping\Driver\MappingDriverChain">
      <call method="addDriver">
        <argument type="service" id="doctrine.orm.default_attribute_metadata_driver"/>
        <argument>App\Entity</argument>
      </call>
    </service>
    <service id=".errored..service_locator.y4_Zrx..Symfony\Component\Config\Loader\LoaderInterface" class="Symfony\Component\Config\Loader\LoaderInterface">
      <tag name="container.error" message="Cannot autowire service &quot;.service_locator.y4_Zrx.&quot;: it needs an instance of &quot;Symfony\Component\Config\Loader\LoaderInterface&quot; but this type has been excluded from autowiring."/>
    </service>
    <service id=".console.command.about.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>about</argument>
      <argument type="collection"/>
      <argument>Display information about the current project</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.about"/>
    </service>
    <service id=".console.command.assets_install.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>assets:install</argument>
      <argument type="collection"/>
      <argument>Install bundle's web assets under a public directory</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.assets_install"/>
    </service>
    <service id=".console.command.cache_clear.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:clear</argument>
      <argument type="collection"/>
      <argument>Clear the cache</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_clear"/>
    </service>
    <service id=".console.command.cache_pool_clear.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:pool:clear</argument>
      <argument type="collection"/>
      <argument>Clear cache pools</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_pool_clear"/>
    </service>
    <service id=".console.command.cache_pool_prune.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:pool:prune</argument>
      <argument type="collection"/>
      <argument>Prune cache pools</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_pool_prune"/>
    </service>
    <service id=".console.command.cache_pool_invalidate_tags.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:pool:invalidate-tags</argument>
      <argument type="collection"/>
      <argument>Invalidate cache tags for all or a specific pool</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_pool_invalidate_tags"/>
    </service>
    <service id=".console.command.cache_pool_delete.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:pool:delete</argument>
      <argument type="collection"/>
      <argument>Delete an item from a cache pool</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_pool_delete"/>
    </service>
    <service id=".console.command.cache_pool_list.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:pool:list</argument>
      <argument type="collection"/>
      <argument>List available cache pools</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_pool_list"/>
    </service>
    <service id=".console.command.cache_warmup.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>cache:warmup</argument>
      <argument type="collection"/>
      <argument>Warm up an empty cache</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.cache_warmup"/>
    </service>
    <service id=".console.command.config_debug.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>debug:config</argument>
      <argument type="collection"/>
      <argument>Dump the current configuration for an extension</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.config_debug"/>
    </service>
    <service id=".console.command.config_dump_reference.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>config:dump-reference</argument>
      <argument type="collection"/>
      <argument>Dump the default configuration for an extension</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.config_dump_reference"/>
    </service>
    <service id=".console.command.container_debug.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>debug:container</argument>
      <argument type="collection"/>
      <argument>Display current services for an application</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.container_debug"/>
    </service>
    <service id=".console.command.container_lint.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>lint:container</argument>
      <argument type="collection"/>
      <argument>Ensure that arguments injected into services match type declarations</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.container_lint"/>
    </service>
    <service id=".console.command.debug_autowiring.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>debug:autowiring</argument>
      <argument type="collection"/>
      <argument>List classes/interfaces you can use for autowiring</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.debug_autowiring"/>
    </service>
    <service id=".console.command.event_dispatcher_debug.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>debug:event-dispatcher</argument>
      <argument type="collection"/>
      <argument>Display configured listeners for an application</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.event_dispatcher_debug"/>
    </service>
    <service id=".console.command.router_debug.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>debug:router</argument>
      <argument type="collection"/>
      <argument>Display current routes for an application</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.router_debug"/>
    </service>
    <service id=".console.command.router_match.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>router:match</argument>
      <argument type="collection"/>
      <argument>Help debug routes by simulating a path info match</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.router_match"/>
    </service>
    <service id=".console.command.yaml_lint.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>lint:yaml</argument>
      <argument type="collection"/>
      <argument>Lint a YAML file and outputs encountered errors</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.yaml_lint"/>
    </service>
    <service id=".console.command.secrets_set.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>secrets:set</argument>
      <argument type="collection"/>
      <argument>Set a secret in the vault</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.secrets_set"/>
    </service>
    <service id=".console.command.secrets_remove.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>secrets:remove</argument>
      <argument type="collection"/>
      <argument>Remove a secret from the vault</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.secrets_remove"/>
    </service>
    <service id=".console.command.secrets_generate_key.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>secrets:generate-keys</argument>
      <argument type="collection"/>
      <argument>Generate new encryption keys</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.secrets_generate_key"/>
    </service>
    <service id=".console.command.secrets_list.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>secrets:list</argument>
      <argument type="collection"/>
      <argument>List all secrets</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.secrets_list"/>
    </service>
    <service id=".console.command.secrets_decrypt_to_local.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>secrets:decrypt-to-local</argument>
      <argument type="collection"/>
      <argument>Decrypt all secrets and stores them in the local vault</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.secrets_decrypt_to_local"/>
    </service>
    <service id=".console.command.secrets_encrypt_from_local.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>secrets:encrypt-from-local</argument>
      <argument type="collection"/>
      <argument>Encrypt all local secrets to the vault</argument>
      <argument>false</argument>
      <argument type="service_closure" id="console.command.secrets_encrypt_from_local"/>
    </service>
    <service id=".doctrine_migrations.diff_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:diff</argument>
      <argument type="collection"/>
      <argument>Generate a migration by comparing your current database to your mapping information.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.diff_command"/>
    </service>
    <service id=".doctrine_migrations.sync_metadata_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:sync-metadata-storage</argument>
      <argument type="collection"/>
      <argument>Ensures that the metadata storage is at the latest version.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.sync_metadata_command"/>
    </service>
    <service id=".doctrine_migrations.versions_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:list</argument>
      <argument type="collection"/>
      <argument>Display a list of all available migrations and their status.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.versions_command"/>
    </service>
    <service id=".doctrine_migrations.current_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:current</argument>
      <argument type="collection"/>
      <argument>Outputs the current version</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.current_command"/>
    </service>
    <service id=".doctrine_migrations.dump_schema_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:dump-schema</argument>
      <argument type="collection"/>
      <argument>Dump the schema for your database to a migration.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.dump_schema_command"/>
    </service>
    <service id=".doctrine_migrations.execute_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:execute</argument>
      <argument type="collection"/>
      <argument>Execute one or more migration versions up or down manually.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.execute_command"/>
    </service>
    <service id=".doctrine_migrations.generate_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:generate</argument>
      <argument type="collection"/>
      <argument>Generate a blank migration class.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.generate_command"/>
    </service>
    <service id=".doctrine_migrations.latest_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:latest</argument>
      <argument type="collection"/>
      <argument>Outputs the latest version</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.latest_command"/>
    </service>
    <service id=".doctrine_migrations.migrate_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:migrate</argument>
      <argument type="collection"/>
      <argument>Execute a migration to a specified version or the latest available version.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.migrate_command"/>
    </service>
    <service id=".doctrine_migrations.rollup_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:rollup</argument>
      <argument type="collection"/>
      <argument>Rollup migrations by deleting all tracked versions and insert the one version that exists.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.rollup_command"/>
    </service>
    <service id=".doctrine_migrations.status_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:status</argument>
      <argument type="collection"/>
      <argument>View the status of a set of migrations.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.status_command"/>
    </service>
    <service id=".doctrine_migrations.up_to_date_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:up-to-date</argument>
      <argument type="collection"/>
      <argument>Tells you if your schema is up-to-date.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.up_to_date_command"/>
    </service>
    <service id=".doctrine_migrations.version_command.lazy" class="Symfony\Component\Console\Command\LazyCommand">
      <argument>doctrine:migrations:version</argument>
      <argument type="collection"/>
      <argument>Manually add and delete migration versions from the version table.</argument>
      <argument>false</argument>
      <argument type="service_closure" id="doctrine_migrations.version_command"/>
    </service>
    <service id="console.command_loader" class="Symfony\Component\Console\CommandLoader\ContainerCommandLoader" public="true">
      <tag name="container.no_preload"/>
      <argument type="service" id=".service_locator.m9btxDV"/>
      <argument type="collection">
        <argument key="app:fetch-prices">App\Command\FetchPricesCommand</argument>
        <argument key="about">console.command.about</argument>
        <argument key="assets:install">console.command.assets_install</argument>
        <argument key="cache:clear">console.command.cache_clear</argument>
        <argument key="cache:pool:clear">console.command.cache_pool_clear</argument>
        <argument key="cache:pool:prune">console.command.cache_pool_prune</argument>
        <argument key="cache:pool:invalidate-tags">console.command.cache_pool_invalidate_tags</argument>
        <argument key="cache:pool:delete">console.command.cache_pool_delete</argument>
        <argument key="cache:pool:list">console.command.cache_pool_list</argument>
        <argument key="cache:warmup">console.command.cache_warmup</argument>
        <argument key="debug:config">console.command.config_debug</argument>
        <argument key="config:dump-reference">console.command.config_dump_reference</argument>
        <argument key="debug:container">console.command.container_debug</argument>
        <argument key="lint:container">console.command.container_lint</argument>
        <argument key="debug:autowiring">console.command.debug_autowiring</argument>
        <argument key="debug:event-dispatcher">console.command.event_dispatcher_debug</argument>
        <argument key="debug:router">console.command.router_debug</argument>
        <argument key="router:match">console.command.router_match</argument>
        <argument key="lint:yaml">console.command.yaml_lint</argument>
        <argument key="secrets:set">console.command.secrets_set</argument>
        <argument key="secrets:remove">console.command.secrets_remove</argument>
        <argument key="secrets:generate-keys">console.command.secrets_generate_key</argument>
        <argument key="secrets:list">console.command.secrets_list</argument>
        <argument key="secrets:decrypt-to-local">console.command.secrets_decrypt_to_local</argument>
        <argument key="secrets:encrypt-from-local">console.command.secrets_encrypt_from_local</argument>
        <argument key="doctrine:database:create">doctrine.database_create_command</argument>
        <argument key="doctrine:database:drop">doctrine.database_drop_command</argument>
        <argument key="doctrine:query:sql">doctrine.query_sql_command</argument>
        <argument key="dbal:run-sql">Doctrine\DBAL\Tools\Console\Command\RunSqlCommand</argument>
        <argument key="doctrine:cache:clear-metadata">doctrine.cache_clear_metadata_command</argument>
        <argument key="doctrine:cache:clear-query">doctrine.cache_clear_query_cache_command</argument>
        <argument key="doctrine:cache:clear-result">doctrine.cache_clear_result_command</argument>
        <argument key="doctrine:cache:clear-collection-region">doctrine.cache_collection_region_command</argument>
        <argument key="doctrine:mapping:convert">doctrine.mapping_convert_command</argument>
        <argument key="doctrine:schema:create">doctrine.schema_create_command</argument>
        <argument key="doctrine:schema:drop">doctrine.schema_drop_command</argument>
        <argument key="doctrine:ensure-production-settings">doctrine.ensure_production_settings_command</argument>
        <argument key="doctrine:cache:clear-entity-region">doctrine.clear_entity_region_command</argument>
        <argument key="doctrine:mapping:info">doctrine.mapping_info_command</argument>
        <argument key="doctrine:cache:clear-query-region">doctrine.clear_query_region_command</argument>
        <argument key="doctrine:query:dql">doctrine.query_dql_command</argument>
        <argument key="doctrine:schema:update">doctrine.schema_update_command</argument>
        <argument key="doctrine:schema:validate">doctrine.schema_validate_command</argument>
        <argument key="doctrine:mapping:import">doctrine.mapping_import_command</argument>
        <argument key="doctrine:migrations:diff">doctrine_migrations.diff_command</argument>
        <argument key="doctrine:migrations:sync-metadata-storage">doctrine_migrations.sync_metadata_command</argument>
        <argument key="doctrine:migrations:list">doctrine_migrations.versions_command</argument>
        <argument key="doctrine:migrations:current">doctrine_migrations.current_command</argument>
        <argument key="doctrine:migrations:dump-schema">doctrine_migrations.dump_schema_command</argument>
        <argument key="doctrine:migrations:execute">doctrine_migrations.execute_command</argument>
        <argument key="doctrine:migrations:generate">doctrine_migrations.generate_command</argument>
        <argument key="doctrine:migrations:latest">doctrine_migrations.latest_command</argument>
        <argument key="doctrine:migrations:migrate">doctrine_migrations.migrate_command</argument>
        <argument key="doctrine:migrations:rollup">doctrine_migrations.rollup_command</argument>
        <argument key="doctrine:migrations:status">doctrine_migrations.status_command</argument>
        <argument key="doctrine:migrations:up-to-date">doctrine_migrations.up_to_date_command</argument>
        <argument key="doctrine:migrations:version">doctrine_migrations.version_command</argument>
      </argument>
    </service>
    <service id=".service_locator.m9btxDV" class="Symfony\Component\DependencyInjection\ServiceLocator">
      <tag name="container.service_locator"/>
      <argument type="collection">
        <argument key="App\Command\FetchPricesCommand" type="service_closure" id="App\Command\FetchPricesCommand"/>
        <argument key="console.command.about" type="service_closure" id=".console.command.about.lazy"/>
        <argument key="console.command.assets_install" type="service_closure" id=".console.command.assets_install.lazy"/>
        <argument key="console.command.cache_clear" type="service_closure" id=".console.command.cache_clear.lazy"/>
        <argument key="console.command.cache_pool_clear" type="service_closure" id=".console.command.cache_pool_clear.lazy"/>
        <argument key="console.command.cache_pool_prune" type="service_closure" id=".console.command.cache_pool_prune.lazy"/>
        <argument key="console.command.cache_pool_invalidate_tags" type="service_closure" id=".console.command.cache_pool_invalidate_tags.lazy"/>
        <argument key="console.command.cache_pool_delete" type="service_closure" id=".console.command.cache_pool_delete.lazy"/>
        <argument key="console.command.cache_pool_list" type="service_closure" id=".console.command.cache_pool_list.lazy"/>
        <argument key="console.command.cache_warmup" type="service_closure" id=".console.command.cache_warmup.lazy"/>
        <argument key="console.command.config_debug" type="service_closure" id=".console.command.config_debug.lazy"/>
        <argument key="console.command.config_dump_reference" type="service_closure" id=".console.command.config_dump_reference.lazy"/>
        <argument key="console.command.container_debug" type="service_closure" id=".console.command.container_debug.lazy"/>
        <argument key="console.command.container_lint" type="service_closure" id=".console.command.container_lint.lazy"/>
        <argument key="console.command.debug_autowiring" type="service_closure" id=".console.command.debug_autowiring.lazy"/>
        <argument key="console.command.event_dispatcher_debug" type="service_closure" id=".console.command.event_dispatcher_debug.lazy"/>
        <argument key="console.command.router_debug" type="service_closure" id=".console.command.router_debug.lazy"/>
        <argument key="console.command.router_match" type="service_closure" id=".console.command.router_match.lazy"/>
        <argument key="console.command.yaml_lint" type="service_closure" id=".console.command.yaml_lint.lazy"/>
        <argument key="console.command.secrets_set" type="service_closure" id=".console.command.secrets_set.lazy"/>
        <argument key="console.command.secrets_remove" type="service_closure" id=".console.command.secrets_remove.lazy"/>
        <argument key="console.command.secrets_generate_key" type="service_closure" id=".console.command.secrets_generate_key.lazy"/>
        <argument key="console.command.secrets_list" type="service_closure" id=".console.command.secrets_list.lazy"/>
        <argument key="console.command.secrets_decrypt_to_local" type="service_closure" id=".console.command.secrets_decrypt_to_local.lazy"/>
        <argument key="console.command.secrets_encrypt_from_local" type="service_closure" id=".console.command.secrets_encrypt_from_local.lazy"/>
        <argument key="doctrine.database_create_command" type="service_closure" id="doctrine.database_create_command"/>
        <argument key="doctrine.database_drop_command" type="service_closure" id="doctrine.database_drop_command"/>
        <argument key="doctrine.query_sql_command" type="service_closure" id="doctrine.query_sql_command"/>
        <argument key="Doctrine\DBAL\Tools\Console\Command\RunSqlCommand" type="service_closure" id="Doctrine\DBAL\Tools\Console\Command\RunSqlCommand"/>
        <argument key="doctrine.cache_clear_metadata_command" type="service_closure" id="doctrine.cache_clear_metadata_command"/>
        <argument key="doctrine.cache_clear_query_cache_command" type="service_closure" id="doctrine.cache_clear_query_cache_command"/>
        <argument key="doctrine.cache_clear_result_command" type="service_closure" id="doctrine.cache_clear_result_command"/>
        <argument key="doctrine.cache_collection_region_command" type="service_closure" id="doctrine.cache_collection_region_command"/>
        <argument key="doctrine.mapping_convert_command" type="service_closure" id="doctrine.mapping_convert_command"/>
        <argument key="doctrine.schema_create_command" type="service_closure" id="doctrine.schema_create_command"/>
        <argument key="doctrine.schema_drop_command" type="service_closure" id="doctrine.schema_drop_command"/>
        <argument key="doctrine.ensure_production_settings_command" type="service_closure" id="doctrine.ensure_production_settings_command"/>
        <argument key="doctrine.clear_entity_region_command" type="service_closure" id="doctrine.clear_entity_region_command"/>
        <argument key="doctrine.mapping_info_command" type="service_closure" id="doctrine.mapping_info_command"/>
        <argument key="doctrine.clear_query_region_command" type="service_closure" id="doctrine.clear_query_region_command"/>
        <argument key="doctrine.query_dql_command" type="service_closure" id="doctrine.query_dql_command"/>
        <argument key="doctrine.schema_update_command" type="service_closure" id="doctrine.schema_update_command"/>
        <argument key="doctrine.schema_validate_command" type="service_closure" id="doctrine.schema_validate_command"/>
        <argument key="doctrine.mapping_import_command" type="service_closure" id="doctrine.mapping_import_command"/>
        <argument key="doctrine_migrations.diff_command" type="service_closure" id=".doctrine_migrations.diff_command.lazy"/>
        <argument key="doctrine_migrations.sync_metadata_command" type="service_closure" id=".doctrine_migrations.sync_metadata_command.lazy"/>
        <argument key="doctrine_migrations.versions_command" type="service_closure" id=".doctrine_migrations.versions_command.lazy"/>
        <argument key="doctrine_migrations.current_command" type="service_closure" id=".doctrine_migrations.current_command.lazy"/>
        <argument key="doctrine_migrations.dump_schema_command" type="service_closure" id=".doctrine_migrations.dump_schema_command.lazy"/>
        <argument key="doctrine_migrations.execute_command" type="service_closure" id=".doctrine_migrations.execute_command.lazy"/>
        <argument key="doctrine_migrations.generate_command" type="service_closure" id=".doctrine_migrations.generate_command.lazy"/>
        <argument key="doctrine_migrations.latest_command" type="service_closure" id=".doctrine_migrations.latest_command.lazy"/>
        <argument key="doctrine_migrations.migrate_command" type="service_closure" id=".doctrine_migrations.migrate_command.lazy"/>
        <argument key="doctrine_migrations.rollup_command" type="service_closure" id=".doctrine_migrations.rollup_command.lazy"/>
        <argument key="doctrine_migrations.status_command" type="service_closure" id=".doctrine_migrations.status_command.lazy"/>
        <argument key="doctrine_migrations.up_to_date_command" type="service_closure" id=".doctrine_migrations.up_to_date_command.lazy"/>
        <argument key="doctrine_migrations.version_command" type="service_closure" id=".doctrine_migrations.version_command.lazy"/>
      </argument>
    </service>
    <service id="Symfony\Component\DependencyInjection\ParameterBag\ContainerBagInterface" alias="parameter_bag"/>
    <service id="Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface" alias="parameter_bag"/>
    <service id="Symfony\Component\EventDispatcher\EventDispatcherInterface" alias="debug.event_dispatcher"/>
    <service id="Symfony\Contracts\EventDispatcher\EventDispatcherInterface" alias="debug.event_dispatcher"/>
    <service id="Psr\EventDispatcher\EventDispatcherInterface" alias="debug.event_dispatcher"/>
    <service id="Symfony\Component\HttpKernel\HttpKernelInterface" alias="http_kernel"/>
    <service id="Symfony\Component\HttpFoundation\RequestStack" alias="request_stack"/>
    <service id="Symfony\Component\HttpKernel\HttpCache\StoreInterface" alias="http_cache.store"/>
    <service id="Symfony\Component\HttpFoundation\UrlHelper" alias="url_helper"/>
    <service id="Symfony\Component\HttpKernel\KernelInterface" alias="kernel"/>
    <service id="Symfony\Component\Filesystem\Filesystem" alias="filesystem"/>
    <service id="Symfony\Component\HttpKernel\Config\FileLocator" alias="file_locator"/>
    <service id="Symfony\Component\HttpFoundation\UriSigner" alias="uri_signer"/>
    <service id="Symfony\Component\HttpKernel\UriSigner" alias="uri_signer">
      <deprecated package="symfony/framework-bundle" version="6.4">The "%alias_id%" alias is deprecated, use "Symfony\Component\HttpFoundation\UriSigner" instead.</deprecated>
    </service>
    <service id="Symfony\Component\DependencyInjection\ReverseContainer" alias="reverse_container"/>
    <service id="Symfony\Component\String\Slugger\SluggerInterface" alias="slugger"/>
    <service id="Symfony\Component\Clock\ClockInterface" alias="clock"/>
    <service id="Psr\Clock\ClockInterface" alias="clock"/>
    <service id="Symfony\Component\HttpKernel\Fragment\FragmentUriGeneratorInterface" alias="fragment.uri_generator"/>
    <service id="error_renderer.html" alias="error_handler.error_renderer.html"/>
    <service id="error_renderer" alias="error_handler.error_renderer.html"/>
    <service id=".Psr\Container\ContainerInterface $parameter_bag" alias="parameter_bag"/>
    <service id="Psr\Container\ContainerInterface $parameterBag" alias="parameter_bag"/>
    <service id="cache.app_clearer" alias="cache.default_clearer" public="true"/>
    <service id="Psr\Cache\CacheItemPoolInterface" alias="cache.app"/>
    <service id="Symfony\Contracts\Cache\CacheInterface" alias="cache.app"/>
    <service id="Symfony\Contracts\Cache\TagAwareCacheInterface" alias="cache.app.taggable"/>
    <service id="Symfony\Component\ErrorHandler\ErrorRenderer\FileLinkFormatter" alias="debug.file_link_formatter"/>
    <service id="Symfony\Component\Stopwatch\Stopwatch" alias="debug.stopwatch"/>
    <service id="routing.loader.annotation" alias="routing.loader.attribute">
      <deprecated package="symfony/routing" version="6.4">The "%alias_id%" service is deprecated, use the "routing.loader.attribute" service instead.</deprecated>
    </service>
    <service id="routing.loader.annotation.directory" alias="routing.loader.attribute.directory">
      <deprecated package="symfony/routing" version="6.4">The "%alias_id%" service is deprecated, use the "routing.loader.attribute.directory" service instead.</deprecated>
    </service>
    <service id="routing.loader.annotation.file" alias="routing.loader.attribute.file">
      <deprecated package="symfony/routing" version="6.4">The "%alias_id%" service is deprecated, use the "routing.loader.attribute.file" service instead.</deprecated>
    </service>
    <service id="router" alias="router.default" public="true"/>
    <service id="Symfony\Component\Routing\RouterInterface" alias="router.default"/>
    <service id="Symfony\Component\Routing\Generator\UrlGeneratorInterface" alias="router.default"/>
    <service id="Symfony\Component\Routing\Matcher\UrlMatcherInterface" alias="router.default"/>
    <service id="Symfony\Component\Routing\RequestContextAwareInterface" alias="router.default"/>
    <service id="Symfony\Component\Routing\RequestContext" alias="router.request_context"/>
    <service id="annotation_reader" alias="annotations.cached_reader"/>
    <service id="Doctrine\Common\Annotations\Reader" alias="annotations.cached_reader"/>
    <service id="Symfony\Component\PropertyAccess\PropertyAccessorInterface" alias="property_accessor"/>
    <service id="Symfony\Component\PropertyInfo\PropertyAccessExtractorInterface" alias="property_info"/>
    <service id="Symfony\Component\PropertyInfo\PropertyDescriptionExtractorInterface" alias="property_info"/>
    <service id="Symfony\Component\PropertyInfo\PropertyInfoExtractorInterface" alias="property_info"/>
    <service id="Symfony\Component\PropertyInfo\PropertyTypeExtractorInterface" alias="property_info"/>
    <service id="Symfony\Component\PropertyInfo\PropertyListExtractorInterface" alias="property_info"/>
    <service id="Symfony\Component\PropertyInfo\PropertyInitializableExtractorInterface" alias="property_info"/>
    <service id="Symfony\Component\PropertyInfo\PropertyReadInfoExtractorInterface" alias="property_info.reflection_extractor"/>
    <service id="Symfony\Component\PropertyInfo\PropertyWriteInfoExtractorInterface" alias="property_info.reflection_extractor"/>
    <service id="cache.default_redis_provider" alias=".cache_connection.GD_MSZC"/>
    <service id="cache.default_memcached_provider" alias=".cache_connection.JKE6keX"/>
    <service id="cache.default_doctrine_dbal_provider" alias="doctrine.dbal.default_connection"/>
    <service id="Doctrine\DBAL\Connection" alias="doctrine.dbal.default_connection"/>
    <service id="Doctrine\Persistence\ManagerRegistry" alias="doctrine"/>
    <service id="Doctrine\Common\Persistence\ManagerRegistry" alias="doctrine"/>
    <service id="database_connection" alias="doctrine.dbal.default_connection" public="true"/>
    <service id="doctrine.dbal.event_manager" alias="doctrine.dbal.default_connection.event_manager"/>
    <service id=".Doctrine\DBAL\Connection $default.connection" alias="doctrine.dbal.default_connection"/>
    <service id="Doctrine\DBAL\Connection $defaultConnection" alias="doctrine.dbal.default_connection"/>
    <service id="Doctrine\ORM\EntityManagerInterface" alias="doctrine.orm.default_entity_manager"/>
    <service id="doctrine.orm.metadata.annotation_reader" alias="annotations.cached_reader"/>
    <service id="doctrine.orm.entity_manager" alias="doctrine.orm.default_entity_manager" public="true"/>
    <service id="doctrine.orm.default_metadata_cache" alias="cache.doctrine.orm.default.metadata"/>
    <service id="doctrine.orm.default_result_cache" alias="cache.doctrine.orm.default.result"/>
    <service id="doctrine.orm.default_query_cache" alias="cache.doctrine.orm.default.query"/>
    <service id=".Doctrine\ORM\EntityManagerInterface $default.entity_manager" alias="doctrine.orm.default_entity_manager"/>
    <service id="Doctrine\ORM\EntityManagerInterface $defaultEntityManager" alias="doctrine.orm.default_entity_manager"/>
    <service id="doctrine.orm.default_entity_manager.event_manager" alias="doctrine.dbal.default_connection.event_manager"/>
    <service id="doctrine.migrations.metadata_storage" alias="doctrine.migrations.storage.table_storage"/>
    <service id="container.env_var_processors_locator" alias=".service_locator.w7.f4fT" public="true"/>
    <service id="argument_resolver.controller_locator" alias=".service_locator.2JZTdhn"/>
    <service id="doctrine.id_generator_locator" alias=".service_locator.KLVvNIq"/>
    <service id="Psr\Log\LoggerInterface" alias="logger"/>
    <service id=".service_locator.bJ.4HC5" alias=".service_locator.w7.f4fT"/>
    <service id=".service_locator.sWM0iPT" alias=".service_locator.2JZTdhn"/>
    <service id=".service_locator.ZSWSSQW" alias=".service_locator.m4yXcy."/>
    <service id=".service_locator.jUv.zyj" alias=".service_locator.O2p6Lk7"/>
    <service id="event_dispatcher" alias="debug.event_dispatcher" public="true"/>
    <service id="controller_resolver" alias="debug.controller_resolver"/>
    <service id="argument_resolver" alias="debug.argument_resolver"/>
    <service id="doctrine.migrations.migrations_factory" alias="doctrine.migrations.container_aware_migrations_factory"/>
    <service id="doctrine.orm.default_metadata_driver" alias=".doctrine.orm.default_metadata_driver"/>
    <service id=".service_locator.gFlme_s" alias=".service_locator.y4_Zrx."/>
    <service id=".service_locator.KL3pAWR" alias=".service_locator.O2p6Lk7"/>
    <service id=".service_locator.3bxtozr" alias=".service_locator._ju.bgC"/>
  </services>
</container>
