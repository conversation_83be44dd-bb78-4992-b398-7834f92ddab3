<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\Container0bznZcM\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/Container0bznZcM/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/Container0bznZcM.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\Container0bznZcM\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \Container0bznZcM\App_KernelDevDebugContainer([
    'container.build_hash' => '0bznZcM',
    'container.build_id' => '02cf990e',
    'container.build_time' => 1751506738,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'Container0bznZcM');
