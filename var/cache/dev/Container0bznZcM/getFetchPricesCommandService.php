<?php

namespace Container0bznZcM;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getFetchPricesCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\FetchPricesCommand' shared autowired service.
     *
     * @return \App\Command\FetchPricesCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).'/vendor/symfony/console/Command/Command.php';
        include_once \dirname(__DIR__, 4).'/src/Command/FetchPricesCommand.php';
        include_once \dirname(__DIR__, 4).'/src/Service/PriceFetcherService.php';
        include_once \dirname(__DIR__, 4).'/src/Service/MockApiService.php';
        include_once \dirname(__DIR__, 4).'/src/Mapper/ApiMapperInterface.php';
        include_once \dirname(__DIR__, 4).'/src/Mapper/Api1Mapper.php';
        include_once \dirname(__DIR__, 4).'/src/Mapper/Api2Mapper.php';

        $a = ($container->privates['logger'] ?? self::getLoggerService($container));

        $container->privates['App\\Command\\FetchPricesCommand'] = $instance = new \App\Command\FetchPricesCommand(new \App\Service\PriceFetcherService(new \App\Service\MockApiService($a), ($container->privates['App\\Repository\\ProductPriceRepository'] ?? $container->load('getProductPriceRepositoryService')), $a, ['api1' => new \App\Mapper\Api1Mapper(), 'api2' => new \App\Mapper\Api2Mapper()]));

        $instance->setName('app:fetch-prices');

        return $instance;
    }
}
