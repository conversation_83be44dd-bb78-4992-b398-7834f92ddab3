<?php

namespace ContainerEYYo7Cy;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPriceControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\PriceController' shared autowired service.
     *
     * @return \App\Controller\PriceController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).'/vendor/symfony/framework-bundle/Controller/AbstractController.php';
        include_once \dirname(__DIR__, 4).'/src/Controller/PriceController.php';

        $container->services['App\\Controller\\PriceController'] = $instance = new \App\Controller\PriceController(($container->privates['logger'] ?? self::getLoggerService($container)), ($container->privates['App\\Repository\\ProductPriceRepository'] ?? $container->load('getProductPriceRepositoryService')));

        $instance->setContainer((new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'http_kernel' => ['services', 'http_kernel', 'getHttpKernelService', false],
            'parameter_bag' => ['privates', 'parameter_bag', 'getParameterBagService', false],
            'request_stack' => ['services', 'request_stack', 'getRequestStackService', false],
            'router' => ['services', 'router', 'getRouterService', false],
        ], [
            'http_kernel' => '?',
            'parameter_bag' => '?',
            'request_stack' => '?',
            'router' => '?',
        ]))->withContext('App\\Controller\\PriceController', $container));

        return $instance;
    }
}
