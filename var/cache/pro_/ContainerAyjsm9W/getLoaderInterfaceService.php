<?php

namespace ContainerAyjsm9W;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/*
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getLoaderInterfaceService extends App_KernelProdContainer
{
    /*
     * Gets the private '.errored..service_locator.y4_Zrx..Symfony\Component\Config\Loader\LoaderInterface' shared service.
     *
     * @return \Symfony\Component\Config\Loader\LoaderInterface
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.y4_Zrx.": it needs an instance of "Symfony\\Component\\Config\\Loader\\LoaderInterface" but this type has been excluded from autowiring.');
    }
}
