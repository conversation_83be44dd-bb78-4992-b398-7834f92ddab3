<?php

declare(strict_types=1);

namespace App\Middleware;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\HttpFoundation\Response;

class AuthMiddleware
{
    private const string API_KEY = 'secure-key-123';

    public function __construct(
        private readonly HttpKernelInterface $app,
        private readonly LoggerInterface $logger
    ) {
    }

    public function handle(
        RequestEvent $requestEvent,
        int $type = HttpKernelInterface::MAIN_REQUEST,
        ?bool $catch = true
    ): Response {
        $request = $requestEvent->getRequest();
        $apiKey = $request->headers->get('X-API-Key');
        if ($apiKey !== self::API_KEY) {
            $this->logger->warning('Unauthorized API access attempt');
            return new JsonResponse(['error' => 'Unauthorized'], 401);
        }

        return $this->app->handle($request, $type, $catch);
    }
}
