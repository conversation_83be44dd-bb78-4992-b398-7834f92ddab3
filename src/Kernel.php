<?php

namespace App;

use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Routing\Loader\AnnotationFileLoader;
use Symfony\Component\Routing\Loader\Configurator\RoutingConfigurator;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    protected function configureContainer(ContainerBuilder $container, LoaderInterface $loader): void
    {
       $container->loadFromExtension('doctrine', [
            'dbal' => [
                'url' => $_ENV['DATABASE_URL'],
            ],
            'orm' => [
                'auto_generate_proxy_classes' => true,
                'naming_strategy' => 'doctrine.orm.naming_strategy.underscore_number_aware',
                'auto_mapping' => true,
                'mappings' => [
                    'App' => [
                        'is_bundle' => false,
                        'type' => 'attribute',
                        'dir' => '%kernel.project_dir%/src/Entity',
                        'prefix' => 'App\Entity',
                        'alias' => 'App',
                    ],
                ],
            ],
        ]);

        $container->loadFromExtension('doctrine_migrations', [
            'migrations_paths' => [
                'App\Migrations' => '%kernel.project_dir%/migrations',
            ],
        ]);

        $loader->load($this->getProjectDir().'/config/services.yaml');
    }

//    protected function configureRoutes(RoutingConfigurator $routes): void
//    {
//        $routes->import($this->getProjectDir().'/src/Controller/', 'annotation');
//    }
}